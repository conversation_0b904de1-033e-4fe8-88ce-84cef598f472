namespace Rolla.Shared.Kernel.Domain;

public abstract class AggregateRoot : Entity, IAggregateRoot, IAuditable
{
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int Version { get; protected set; }
}

public abstract class AggregateRoot<TId> : Entity<TId>, IAggregateRoot, IAuditable where TId : notnull
{
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int Version { get; protected set; }
}

public interface IAggregateRoot
{
    DateTime CreatedAt { get; }
    DateTime? UpdatedAt { get; }
    int Version { get; }
}
