using System.ComponentModel.DataAnnotations;

namespace Rolla.BuildingBlocks.Application.Validation;

/// <summary>
/// Custom validation attribute for page size to ensure proper error handling
/// </summary>
public class PageSizeValidationAttribute : ValidationAttribute
{
    private readonly int _minValue;
    private readonly int _maxValue;

    public PageSizeValidationAttribute(int minValue = 1, int maxValue = 100)
    {
        _minValue = minValue;
        _maxValue = maxValue;
        ErrorMessage = $"Page size must be between {_minValue} and {_maxValue}";
    }

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true; // Let Required attribute handle null values

        if (value is int intValue)
        {
            return intValue >= _minValue && intValue <= _maxValue;
        }

        return false;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"Page size must be between {_minValue} and {_maxValue}";
    }
}
