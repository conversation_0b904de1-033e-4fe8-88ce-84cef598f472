<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0"/>
        <PackageReference Include="FluentValidation" Version="11.11.0"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5"/>
        <PackageReference Include="OpenSearch.Client" Version="1.8.0"/>
    </ItemGroup>

</Project>