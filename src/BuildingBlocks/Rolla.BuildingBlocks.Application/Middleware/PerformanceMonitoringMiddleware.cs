using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace Rolla.BuildingBlocks.Application.Middleware;

/// <summary>
/// Middleware to monitor API performance and log slow requests
/// </summary>
public class PerformanceMonitoringMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceMonitoringMiddleware> _logger;
    private const int SlowRequestThresholdMs = 1000; // 1 second

    public PerformanceMonitoringMiddleware(RequestDelegate next, ILogger<PerformanceMonitoringMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var requestPath = context.Request.Path.Value;
        var requestMethod = context.Request.Method;

        // Add response header before processing
        context.Response.OnStarting(() =>
        {
            stopwatch.Stop();
            var elapsedMs = stopwatch.ElapsedMilliseconds;
            context.Response.Headers.Add("X-Response-Time-Ms", elapsedMs.ToString());
            return Task.CompletedTask;
        });

        try
        {
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            var elapsedMs = stopwatch.ElapsedMilliseconds;

            // Log performance metrics
            if (elapsedMs > SlowRequestThresholdMs)
            {
                _logger.LogWarning(
                    "Slow request detected: {Method} {Path} took {ElapsedMs}ms. " +
                    "Query: {QueryString}, Status: {StatusCode}",
                    requestMethod,
                    requestPath,
                    elapsedMs,
                    context.Request.QueryString.Value,
                    context.Response.StatusCode);
            }
            else
            {
                _logger.LogInformation(
                    "Request completed: {Method} {Path} in {ElapsedMs}ms, Status: {StatusCode}",
                    requestMethod,
                    requestPath,
                    elapsedMs,
                    context.Response.StatusCode);
            }
        }
    }
}
