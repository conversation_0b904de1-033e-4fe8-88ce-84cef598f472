namespace Rolla.BuildingBlocks.Application.Common;

public class ApiResult<T>
{
    public bool Success { get; private set; }
    public T? Data { get; private set; }
    public int StatusCode { get; private set; }
    public string? Message { get; private set; }
    public IEnumerable<string> Errors { get; private set; } = [];
    public DateTime Timestamp { get; private set; } = DateTime.UtcNow;

    private ApiResult() { }

    public static ApiResult<T> Ok(T data, string? message = null)
    {
        return new ApiResult<T>
        {
            Success = true,
            Data = data,
            StatusCode = 200,
            Message = message ?? "Request completed successfully"
        };
    }

    public static ApiResult<T> Created(T data, string? message = null)
    {
        return new ApiResult<T>
        {
            Success = true,
            Data = data,
            StatusCode = 201,
            Message = message ?? "Resource created successfully"
        };
    }

    public static ApiResult<T> NotFound(string? message = null)
    {
        return new ApiResult<T>
        {
            Success = false,
            StatusCode = 404,
            Message = message ?? "Resource not found",
            Errors = [message ?? "Resource not found"]
        };
    }

    public static ApiResult<T> BadRequest(IEnumerable<string> errors, string? message = null)
    {
        return new ApiResult<T>
        {
            Success = false,
            StatusCode = 400,
            Message = message ?? "Bad request",
            Errors = errors
        };
    }

    public static ApiResult<T> BadRequest(string error, string? message = null)
    {
        return BadRequest([error], message);
    }

    public static ApiResult<T> InternalServerError(string? message = null)
    {
        return new ApiResult<T>
        {
            Success = false,
            StatusCode = 500,
            Message = message ?? "Internal server error",
            Errors = [message ?? "An unexpected error occurred"]
        };
    }
}

public class ApiResult
{
    public bool Success { get; private set; }
    public int StatusCode { get; private set; }
    public string? Message { get; private set; }
    public IEnumerable<string> Errors { get; private set; } = [];
    public DateTime Timestamp { get; private set; } = DateTime.UtcNow;

    private ApiResult() { }

    public static ApiResult Ok(string? message = null)
    {
        return new ApiResult
        {
            Success = true,
            StatusCode = 200,
            Message = message ?? "Request completed successfully"
        };
    }

    public static ApiResult NoContent(string? message = null)
    {
        return new ApiResult
        {
            Success = true,
            StatusCode = 204,
            Message = message ?? "No content"
        };
    }

    public static ApiResult NotFound(string? message = null)
    {
        return new ApiResult
        {
            Success = false,
            StatusCode = 404,
            Message = message ?? "Resource not found",
            Errors = [message ?? "Resource not found"]
        };
    }

    public static ApiResult BadRequest(IEnumerable<string> errors, string? message = null)
    {
        return new ApiResult
        {
            Success = false,
            StatusCode = 400,
            Message = message ?? "Bad request",
            Errors = errors
        };
    }

    public static ApiResult BadRequest(string error, string? message = null)
    {
        return BadRequest([error], message);
    }

    public static ApiResult InternalServerError(string? message = null)
    {
        return new ApiResult
        {
            Success = false,
            StatusCode = 500,
            Message = message ?? "Internal server error",
            Errors = [message ?? "An unexpected error occurred"]
        };
    }
}
