namespace Rolla.BuildingBlocks.Application.Validation;

public class Result<T>
{
    public bool IsSuccess { get; private set; }
    public T? Value { get; private set; }
    public IEnumerable<string> Errors { get; private set; } = [];

    private Result()
    {
    }

    public static Result<T> Success(T value)
    {
        return new Result<T>
        {
            IsSuccess = true,
            Value = value
        };
    }

    public static Result<T> Failure(string error)
    {
        return new Result<T>
        {
            IsSuccess = false,
            Errors = new List<string> { error }
        };
    }

    public static Result<T> Failure(List<string> errors)
    {
        return new Result<T>
        {
            IsSuccess = false,
            Errors = errors
        };
    }

    public TResult Match<TResult>(
        Func<T, TResult> onSuccess,
        Func<List<string>, TResult> onFailure)
    {
        return IsSuccess ? onSuccess(Value!) : onFailure(Errors.ToList());
    }
}
