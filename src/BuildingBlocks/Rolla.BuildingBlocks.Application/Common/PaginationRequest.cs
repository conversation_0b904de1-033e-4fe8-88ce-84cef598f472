using System.ComponentModel.DataAnnotations;
using Rolla.BuildingBlocks.Application.Validation;

namespace Rolla.BuildingBlocks.Application.Common;

public class PaginationRequest
{
    private const int MaxPageSize = 100;
    private const int DefaultPageSize = 10;

    [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
    public int PageNumber { get; set; } = 1;

    [PageSizeValidation(1, MaxPageSize)]
    public int PageSize { get; set; } = DefaultPageSize;

    public int Skip => (PageNumber - 1) * PageSize;
    public int Take => PageSize;
}
