using Microsoft.AspNetCore.Mvc;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Validation;

namespace Rolla.BuildingBlocks.Application.Extensions;

public static class ResultExtensions
{
    public static IActionResult ToApiResult<T>(this Result<T> result, string? successMessage = null)
    {
        if (result.IsSuccess)
        {
            var apiResult = ApiResult<T>.Ok(result.Value!, successMessage);
            return new ObjectResult(apiResult) { StatusCode = apiResult.StatusCode };
        }

        var errorResult = ApiResult<T>.BadRequest(result.Errors);
        return new ObjectResult(errorResult) { StatusCode = errorResult.StatusCode };
    }

    public static IActionResult ToApiCreatedResult<T>(this Result<T> result, string? successMessage = null)
    {
        if (result.IsSuccess)
        {
            var apiResult = ApiResult<T>.Created(result.Value!, successMessage);
            return new ObjectResult(apiResult) { StatusCode = apiResult.StatusCode };
        }

        var errorResult = ApiResult<T>.BadRequest(result.Errors);
        return new ObjectResult(errorResult) { StatusCode = errorResult.StatusCode };
    }

    public static IActionResult ToApiNoContentResult(this Result<bool> result, string? successMessage = null)
    {
        if (result.IsSuccess)
        {
            var apiResult = ApiResult.NoContent(successMessage);
            return new ObjectResult(apiResult) { StatusCode = apiResult.StatusCode };
        }

        var errorResult = ApiResult.BadRequest(result.Errors);
        return new ObjectResult(errorResult) { StatusCode = errorResult.StatusCode };
    }

    public static IActionResult ToApiNotFoundResult<T>(this Result<T> result, string? notFoundMessage = null)
    {
        if (result.IsSuccess)
        {
            var apiResult = ApiResult<T>.Ok(result.Value!);
            return new ObjectResult(apiResult) { StatusCode = apiResult.StatusCode };
        }

        var errorResult = ApiResult<T>.NotFound(notFoundMessage ?? result.Errors.FirstOrDefault());
        return new ObjectResult(errorResult) { StatusCode = errorResult.StatusCode };
    }
}