<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AWSSDK.SQS" Version="4.0.0.14"/>
        <PackageReference Include="OpenSearch.Client" Version="1.8.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.13"/>
        <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0"/>
        <PackageReference Include="Polly" Version="8.5.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Shared\Rolla.Shared.Kernel\Rolla.Shared.Kernel.csproj"/>
        <ProjectReference Include="..\Rolla.BuildingBlocks.Application\Rolla.BuildingBlocks.Application.csproj"/>
    </ItemGroup>

</Project>
