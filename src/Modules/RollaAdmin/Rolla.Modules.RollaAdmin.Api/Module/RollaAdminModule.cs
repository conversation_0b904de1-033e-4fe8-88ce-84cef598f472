using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Rolla.BuildingBlocks.Application.Modules;
using Rolla.Modules.RollaAdmin.Application.Configuration;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Guidelines;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Prompts;
using Rolla.Modules.RollaAdmin.Application.Interfaces.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Topics;
using Rolla.Modules.RollaAdmin.Application.Services.Guidelines;
using Rolla.Modules.RollaAdmin.Application.Services.Prompts;
using Rolla.Modules.RollaAdmin.Application.Services.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.Services.Topics;
using Rolla.Modules.RollaAdmin.Application.Validators.Guidelines;
using Rolla.Modules.RollaAdmin.Application.Validators.Prompts;
using Rolla.Modules.RollaAdmin.Application.Validators.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.Validators.Topics;
using Rolla.Modules.RollaAdmin.Infrastructure.Database;
using Rolla.Modules.RollaAdmin.Infrastructure.Persistence.Guidelines;
using Rolla.Modules.RollaAdmin.Infrastructure.Persistence.Prompts;
using Rolla.Modules.RollaAdmin.Infrastructure.Persistence.Topics;
using Rolla.Modules.RollaAdmin.Infrastructure.Repositories.PromptTriggers;

namespace Rolla.Modules.RollaAdmin.Api.Module;

public class RollaAdminModule : ModuleBase
{

    public override void RegisterModule(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<RollaAdminOptions>(configuration.GetSection(RollaAdminOptions.SectionName));

        services.AddDbContext<RollaAdminDbContext>(options =>
            options.UseMySql(
                configuration.GetConnectionString("DefaultConnection"),
                ServerVersion.AutoDetect(configuration.GetConnectionString("DefaultConnection"))));

        services.AddScoped<IPromptRepository, PromptRepository>();
        services.AddScoped<IPromptService, PromptService>();

        services.AddScoped<IGuidelineRepository, GuidelineRepository>();
        services.AddScoped<IGuidelineService, GuidelineService>();

        services.AddScoped<ITopicRepository, TopicRepository>();
        services.AddScoped<ITopicService, TopicService>();

        services.AddScoped<IPromptTriggerRepository, PromptTriggerRepository>();
        services.AddScoped<IPromptTriggerService, PromptTriggerService>();

        services.AddValidatorsFromAssembly(typeof(CreatePromptDtoValidator).Assembly);
        services.AddValidatorsFromAssembly(typeof(CreateGuidelineDtoValidator).Assembly);
        services.AddValidatorsFromAssembly(typeof(CreateTopicDtoValidator).Assembly);
        services.AddValidatorsFromAssembly(typeof(CreatePromptTriggerDtoValidator).Assembly);
    }

    public override void UseModule(IApplicationBuilder app)
    {
    }

}
