using System.ComponentModel.DataAnnotations;
using Rolla.Modules.RollaAdmin.Application.Constants.Topics;

namespace Rolla.Modules.RollaAdmin.Api.Contracts.Topics;

public record CreateTopicRequest
{
    [Required(ErrorMessage = ValidationMessages.NameRequired)]
    [MaxLength(ValidationConstants.NameMaxLength, ErrorMessage = ValidationMessages.NameMaxLength)]
    public required string Name { get; init; }

    [MaxLength(ValidationConstants.DescriptionMaxLength, ErrorMessage = ValidationMessages.DescriptionMaxLength)]
    public string? Description { get; init; }

    [Required(ErrorMessage = ValidationMessages.PromptRequired)]
    [MaxLength(ValidationConstants.PromptMaxLength, ErrorMessage = ValidationMessages.PromptMaxLength)]
    public required string Prompt { get; init; }

    [Range(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue, ErrorMessage = ValidationMessages.PriorityRange)]
    public int Priority { get; init; }
}
