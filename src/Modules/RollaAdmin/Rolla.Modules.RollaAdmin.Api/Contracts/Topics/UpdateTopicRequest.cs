using System.ComponentModel.DataAnnotations;
using Rolla.Modules.RollaAdmin.Application.Constants.Topics;

namespace Rolla.Modules.RollaAdmin.Api.Contracts.Topics;

public record UpdateTopicRequest
{
    [MaxLength(ValidationConstants.NameMaxLength, ErrorMessage = ValidationMessages.NameMaxLength)]
    public string? Name { get; init; }

    [MaxLength(ValidationConstants.DescriptionMaxLength, ErrorMessage = ValidationMessages.DescriptionMaxLength)]
    public string? Description { get; init; }

    [MaxLength(ValidationConstants.PromptMaxLength, ErrorMessage = ValidationMessages.PromptMaxLength)]
    public string? Prompt { get; init; }

    [Range(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue, ErrorMessage = ValidationMessages.PriorityRange)]
    public int? Priority { get; init; }
}
