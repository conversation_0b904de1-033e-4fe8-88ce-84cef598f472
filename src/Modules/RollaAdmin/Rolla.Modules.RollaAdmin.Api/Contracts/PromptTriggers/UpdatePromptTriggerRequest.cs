using System.ComponentModel.DataAnnotations;
using Rolla.Modules.RollaAdmin.Api.Validation;
using Rolla.Modules.RollaAdmin.Application.Constants.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Api.Contracts.PromptTriggers;

[AtLeastOneField(ErrorMessage = ValidationMessages.HaveAtLeastOneField)]
public record UpdatePromptTriggerRequest
{
    [MaxLength(ValidationConstants.NameMaxLength, ErrorMessage = ValidationMessages.NameMaxLength)]
    public string? Name { get; init; }

    [MaxLength(ValidationConstants.DescriptionMaxLength, ErrorMessage = ValidationMessages.DescriptionMaxLength)]
    public string? Description { get; init; }

    [Range(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue, ErrorMessage = ValidationMessages.PriorityRange)]
    public int? Priority { get; init; }

    [Range(ValidationConstants.InsightNumberMinValue, ValidationConstants.InsightNumberMaxValue, ErrorMessage = ValidationMessages.InsightNumberRange)]
    public int? InsightNumber { get; init; }

    [Range(ValidationConstants.CooldownMinutesMinValue, ValidationConstants.CooldownMinutesMaxValue, ErrorMessage = ValidationMessages.CooldownMinutesRange)]
    public int? CooldownMinutes { get; init; }

    [Range(ValidationConstants.DailyLimitMinValue, ValidationConstants.DailyLimitMaxValue, ErrorMessage = ValidationMessages.DailyLimitRange)]
    public int? DailyLimit { get; init; }

    public TriggerCondition? TriggerCondition { get; init; }
    public ConditionType? ConditionType { get; init; }

    // Score Value specific properties
    public ScoreType? ScoreType { get; init; }
    public OperatorType? ScoreOperator { get; init; }

    [MaxLength(ValidationConstants.ValueMaxLength, ErrorMessage = ValidationMessages.ValueMaxLength)]
    public string? ScoreValue { get; init; }

    // Time of Day specific properties
    public TimeOnly? StartTime { get; init; }
    public TimeOnly? EndTime { get; init; }

    // Metric Value specific properties
    public MetricType? MetricType { get; init; }
    public OperatorType? MetricOperator { get; init; }

    [MaxLength(ValidationConstants.ValueMaxLength, ErrorMessage = ValidationMessages.ValueMaxLength)]
    public string? MetricValue { get; init; }

    // Associated Topics
    public List<Guid>? AssociatedTopicIds { get; init; }
}
