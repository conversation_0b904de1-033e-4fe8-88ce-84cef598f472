using System.ComponentModel.DataAnnotations;
using Rolla.Modules.RollaAdmin.Application.Constants.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Api.Contracts.PromptTriggers;

public record CreatePromptTriggerRequest
{
    [Required(ErrorMessage = ValidationMessages.NameRequired)]
    [MaxLength(ValidationConstants.NameMaxLength, ErrorMessage = ValidationMessages.NameMaxLength)]
    public required string Name { get; init; }
    [MaxLength(ValidationConstants.DescriptionMaxLength, ErrorMessage = ValidationMessages.DescriptionMaxLength)]
    public string? Description { get; init; }
    [Range(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue, ErrorMessage = ValidationMessages.PriorityRange)]
    public int Priority { get; init; } = 1;
    [Range(ValidationConstants.InsightNumberMinValue, ValidationConstants.InsightNumberMaxValue, ErrorMessage = ValidationMessages.InsightNumberRange)]
    public int InsightNumber { get; init; }
    [Range(ValidationConstants.CooldownMinutesMinValue, ValidationConstants.CooldownMinutesMaxValue, ErrorMessage = ValidationMessages.CooldownMinutesRange)]
    public int CooldownMinutes { get; init; }
    [Range(ValidationConstants.DailyLimitMinValue, ValidationConstants.DailyLimitMaxValue, ErrorMessage = ValidationMessages.DailyLimitRange)]
    public int DailyLimit { get; init; }
    [Required(ErrorMessage = ValidationMessages.TriggerConditionRequired)]
    public TriggerCondition TriggerCondition { get; init; }
    [Required(ErrorMessage = ValidationMessages.ConditionTypeRequired)]
    public ConditionType ConditionType { get; init; }
    public ScoreType? ScoreType { get; init; }
    public OperatorType? ScoreOperator { get; init; }
    [MaxLength(ValidationConstants.ValueMaxLength, ErrorMessage = ValidationMessages.ValueMaxLength)]
    public string? ScoreValue { get; init; }
    public TimeOnly? StartTime { get; init; }
    public TimeOnly? EndTime { get; init; }
    public MetricType? MetricType { get; init; }
    public OperatorType? MetricOperator { get; init; }
    [MaxLength(ValidationConstants.ValueMaxLength, ErrorMessage = ValidationMessages.ValueMaxLength)]
    public string? MetricValue { get; init; }
    public List<Guid> AssociatedTopicIds { get; init; } = new();
}
