using System.ComponentModel.DataAnnotations;
using Rolla.Modules.RollaAdmin.Application.Constants.Prompts;

namespace Rolla.Modules.RollaAdmin.Api.Contracts.Prompts;

public record CreatePromptRequestComposition
{
    [Required(ErrorMessage = ValidationMessages.NameRequired)]
    [MaxLength(ValidationConstants.NameMaxLength, ErrorMessage = ValidationMessages.NameMaxLength)]
    public required string Name { get; init; }

    [MaxLength(ValidationConstants.DescriptionMaxLength, ErrorMessage = ValidationMessages.DescriptionMaxLength)]
    public string? Description { get; init; }

    [Required(ErrorMessage = ValidationMessages.SystemPromptRequired)]
    [MaxLength(ValidationConstants.SystemPromptMaxLength, ErrorMessage = ValidationMessages.SystemPromptMaxLength)]
    public required string SystemPromptContent { get; init; }
}
