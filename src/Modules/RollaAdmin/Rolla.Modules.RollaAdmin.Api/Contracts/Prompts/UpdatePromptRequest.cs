using System.ComponentModel.DataAnnotations;
using Rolla.Modules.RollaAdmin.Application.Constants.Prompts;

namespace Rolla.Modules.RollaAdmin.Api.Contracts.Prompts;

public record UpdatePromptRequestComposition
{
    [MaxLength(ValidationConstants.NameMaxLength, ErrorMessage = ValidationMessages.NameMaxLength)]
    public string? Name { get; init; }

    [MaxLength(ValidationConstants.DescriptionMaxLength, ErrorMessage = ValidationMessages.DescriptionMaxLength)]
    public string? Description { get; init; }

    [MaxLength(ValidationConstants.SystemPromptMaxLength, ErrorMessage = ValidationMessages.SystemPromptMaxLength)]
    public string? SystemPromptContent { get; init; }
}
