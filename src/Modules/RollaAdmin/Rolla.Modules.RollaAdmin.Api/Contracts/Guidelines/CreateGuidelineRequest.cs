using System.ComponentModel.DataAnnotations;
using Rolla.Modules.RollaAdmin.Application.Constants.Guidelines;

namespace Rolla.Modules.RollaAdmin.Api.Contracts.Guidelines;

public class CreateGuidelineRequestComposition
{
    [Required(ErrorMessage = ValidationMessages.NameRequired)]
    [MaxLength(ValidationConstants.NameMaxLength, ErrorMessage = ValidationMessages.NameMaxLength)]
    public required string Name { get; set; }

    [MaxLength(ValidationConstants.DescriptionMaxLength, ErrorMessage = ValidationMessages.DescriptionMaxLength)]
    public string? Description { get; set; }

    [Required(ErrorMessage = ValidationMessages.GuidelineContentRequired)]
    [MaxLength(ValidationConstants.PromptGuidelineContentMaxLength, ErrorMessage = ValidationMessages.GuidelineContentMaxLength)]
    public required string PromptGuidelineContent { get; set; }
}
