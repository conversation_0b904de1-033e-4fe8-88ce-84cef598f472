using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Controllers;
using Rolla.Modules.RollaAdmin.Api.Contracts.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.Interfaces.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Api.Controllers;

/// <summary>
/// Manages prompt triggers with comprehensive CRUD operations, conditional logic, and activation features.
/// </summary>
[ApiController]
[Route("api/rolla-admin/prompt-triggers")]
[Produces("application/json")]
[Tags("Rolla Admin - Prompt Management System - Triggers")]
public class PromptTriggerController : BaseController
{
    private readonly IPromptTriggerService _promptTriggerService;

    public PromptTriggerController(ILogger<PromptTriggerController> logger, IPromptTriggerService promptTriggerService)
        : base(logger)
    {
        _promptTriggerService = promptTriggerService;
    }

    /// <summary>
    /// Retrieve all prompt triggers with advanced filtering, search, and pagination
    /// </summary>
    /// <remarks>
    /// Supports comprehensive filtering and search capabilities for prompt triggers:
    ///
    /// <para><strong>Search Features:</strong></para>
    /// - Full-text search across name and description
    /// - Case-insensitive matching with partial word support
    /// - Automatic wildcard matching for flexible queries
    ///
    /// <para><strong>Filter Options:</strong></para>
    /// - **IsActive**: Filter by activation status (true/false/null for all)
    /// - **Priority Range**: Filter by minimum and/or maximum priority values
    /// - **Insight Number**: Filter by specific insight number
    /// - **Trigger Condition**: Filter by ALL or ANY condition logic
    /// - **Condition Type**: Filter by FirstSyncOfDay, ScoreValue, TimeOfDay, or MetricValue
    /// - **Date Ranges**: Filter by creation and update timestamps
    /// - **Sorting**: Multiple sort fields with ascending/descending options
    ///
    /// <para><strong>Pagination:</strong></para>
    /// - Configurable page size (1-100 items per page)
    /// - Efficient offset-based pagination
    /// - Total count and page metadata included
    ///
    /// <para><strong>Condition Types:</strong></para>
    /// - **FirstSyncOfDay**: Triggers on first daily sync (no additional parameters)
    /// - **ScoreValue**: Triggers based on health scores with operators and thresholds
    /// - **TimeOfDay**: Triggers within specific time windows
    /// - **MetricValue**: Triggers based on metric values with operators and thresholds
    ///
    /// <para><strong>Performance Notes:</strong></para>
    /// - Results are optimized for common filter combinations
    /// - Database indexes optimize trigger condition queries
    /// - Large result sets automatically paginated
    /// </remarks>
    /// <param name="searchTerm">Search term for name and description</param>
    /// <param name="isActive">Filter by activation status (optional)</param>
    /// <param name="minPriority">Minimum priority value (optional)</param>
    /// <param name="maxPriority">Maximum priority value (optional)</param>
    /// <param name="insightNumber">Filter by specific insight number (optional)</param>
    /// <param name="triggerCondition">Filter by trigger condition logic (ALL/ANY, optional)</param>
    /// <param name="conditionType">Filter by condition type (FirstSyncOfDay/ScoreValue/TimeOfDay/MetricValue, optional)</param>
    /// <param name="createdAfter">Filter triggers created after this date (optional)</param>
    /// <param name="createdBefore">Filter triggers created before this date (optional)</param>
    /// <param name="updatedAfter">Filter triggers updated after this date (optional)</param>
    /// <param name="updatedBefore">Filter triggers updated before this date (optional)</param>
    /// <param name="sortBy">Sort field: Name, Description, Priority, InsightNumber, CooldownMinutes, DailyLimit, IsActive, TriggerCondition, ConditionType, CreatedAt, UpdatedAt (default: CreatedAt)</param>
    /// <param name="sortDescending">Sort direction: true for descending, false for ascending (default: true)</param>
    /// <param name="pageNumber">Page number (1-based, default: 1)</param>
    /// <param name="pageSize">Items per page (1-100, default: 10)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of prompt triggers matching the specified criteria</returns>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResult<PromptTriggerDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAllPromptTriggers(
        [FromQuery] string? searchTerm,
        [FromQuery] bool? isActive,
        [FromQuery] int? minPriority,
        [FromQuery] int? maxPriority,
        [FromQuery] int? insightNumber,
        [FromQuery] TriggerCondition? triggerCondition,
        [FromQuery] ConditionType? conditionType,
        [FromQuery] DateTime? createdAfter,
        [FromQuery] DateTime? createdBefore,
        [FromQuery] DateTime? updatedAfter,
        [FromQuery] DateTime? updatedBefore,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] bool sortDescending = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var request = new PromptTriggerFilterRequest
        {
            SearchTerm = searchTerm,
            IsActive = isActive,
            MinPriority = minPriority,
            MaxPriority = maxPriority,
            InsightNumber = insightNumber,
            TriggerCondition = triggerCondition,
            ConditionType = conditionType,
            CreatedAfter = createdAfter,
            CreatedBefore = createdBefore,
            UpdatedAfter = updatedAfter,
            UpdatedBefore = updatedBefore,
            SortBy = sortBy,
            SortDescending = sortDescending,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        return await ExecuteAsync(
            () => _promptTriggerService.GetAllPromptTriggersAsync(request, cancellationToken),
            "Prompt triggers retrieved successfully",
            "Failed to retrieve prompt triggers"
        );
    }

    /// <summary>
    /// Retrieve a specific prompt trigger by its unique identifier
    /// </summary>
    /// <remarks>
    /// Returns complete prompt trigger details including:
    /// - Basic information (name, description, priority, insight number)
    /// - Trigger configuration (condition type, trigger logic)
    /// - Condition-specific properties (score/metric types, operators, values, time ranges)
    /// - Associated topics and activation status
    /// - Creation and modification timestamps
    ///
    /// <para><strong>Condition-Specific Data:</strong></para>
    /// - **ScoreValue**: Returns scoreType, scoreOperator, and scoreValue
    /// - **TimeOfDay**: Returns startTime and endTime
    /// - **MetricValue**: Returns metricType, metricOperator, and metricValue
    /// - **FirstSyncOfDay**: Returns only basic trigger information
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// - Trigger detail views in admin interfaces
    /// - Pre-populating edit forms with current values
    /// - API integrations requiring full trigger configuration
    /// - Audit and compliance reporting
    /// - Debugging trigger behavior and conditions
    /// </remarks>
    /// <param name="id">Unique prompt trigger identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Complete prompt trigger details if found</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(PromptTriggerDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetPromptTriggerById(Guid id, CancellationToken cancellationToken = default)
    {
        return await ExecuteNotFoundAsync(
            () => _promptTriggerService.GetPromptTriggerByIdAsync(id, cancellationToken),
            "Prompt trigger not found",
            "Failed to retrieve prompt trigger"
        );
    }

    /// <summary>
    /// Create a new prompt trigger with conditional logic
    /// </summary>
    /// <remarks>
    /// Creates a new prompt trigger with the specified configuration. The trigger will be created in an inactive state
    /// and must be explicitly activated using the activation endpoint.
    ///
    /// <para><strong>Validation Rules:</strong></para>
    /// - Name: Required, maximum 100 characters, must be unique
    /// - Description: Optional, maximum 500 characters
    /// - Priority: Required, must be between 1 and 1000 (lower numbers = higher priority)
    /// - InsightNumber: Required, must be between 1 and 1000
    /// - CooldownMinutes: Required, must be between 0 and 10080 (7 days)
    /// - DailyLimit: Required, must be between 1 and 100
    /// - TriggerCondition: Required (ALL or ANY)
    /// - ConditionType: Required (FirstSyncOfDay, ScoreValue, TimeOfDay, MetricValue)
    ///
    /// <para><strong>Condition-Specific Requirements:</strong></para>
    /// - **ScoreValue**: Requires scoreType, scoreOperator, and scoreValue (unless operator is ChangedDuringDay)
    /// - **TimeOfDay**: Requires startTime and endTime (startTime must be before endTime)
    /// - **MetricValue**: Requires metricType, metricOperator, and metricValue (unless operator is ChangedDuringDay)
    /// - **FirstSyncOfDay**: No additional properties required
    ///
    /// <para><strong>Business Rules:</strong></para>
    /// - Trigger names must be unique across all triggers
    /// - New triggers are created in inactive state
    /// - Creation timestamp automatically set to current UTC time
    /// - Priority determines execution order when multiple triggers are active
    /// - Associated topics list can be empty (applies to all active topics)
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - Creating health score-based triggers for insights
    /// - Setting up time-based reminder triggers
    /// - Configuring metric threshold alerts
    /// - Implementing first-sync-of-day triggers
    /// </remarks>
    /// <param name="request">Prompt trigger creation details with condition-specific properties</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The newly created prompt trigger with ID and timestamps</returns>
    [HttpPost]
    [ProducesResponseType(typeof(PromptTriggerDto), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreatePromptTrigger([FromBody] CreatePromptTriggerRequest request, CancellationToken cancellationToken = default)
    {
        var dto = new CreatePromptTriggerDto
        {
            Name = request.Name,
            Description = request.Description,
            Priority = request.Priority,
            InsightNumber = request.InsightNumber,
            CooldownMinutes = request.CooldownMinutes,
            DailyLimit = request.DailyLimit,
            TriggerCondition = request.TriggerCondition,
            ConditionType = request.ConditionType,
            ScoreType = request.ScoreType,
            ScoreOperator = request.ScoreOperator,
            ScoreValue = request.ScoreValue,
            StartTime = request.StartTime,
            EndTime = request.EndTime,
            MetricType = request.MetricType,
            MetricOperator = request.MetricOperator,
            MetricValue = request.MetricValue,
            AssociatedTopicIds = request.AssociatedTopicIds
        };

        return await ExecuteCreatedAsync(
            () => _promptTriggerService.CreatePromptTriggerAsync(dto, cancellationToken),
            "Prompt trigger created successfully",
            "Failed to create prompt trigger"
        );
    }

    /// <summary>
    /// Update an existing prompt trigger
    /// </summary>
    /// <remarks>
    /// Updates the specified prompt trigger with new values. Only provided fields will be updated,
    /// allowing for partial updates without affecting other properties.
    ///
    /// <para><strong>Validation Rules:</strong></para>
    /// - At least one field must be provided for update
    /// - Name: Maximum 100 characters, must be unique if provided
    /// - Description: Maximum 500 characters if provided
    /// - Priority: Must be between 1 and 1000 if provided
    /// - InsightNumber: Must be between 1 and 1000 if provided
    /// - CooldownMinutes: Must be between 0 and 10080 (7 days) if provided
    /// - DailyLimit: Must be between 1 and 100 if provided
    ///
    /// <para><strong>Business Rules:</strong></para>
    /// - Trigger names must remain unique across all triggers
    /// - ConditionType changes are not supported (create new trigger instead)
    /// - Condition-specific properties are validated based on current conditionType
    /// - UpdatedAt timestamp automatically set to current UTC time
    /// - Activation status is preserved unless explicitly changed
    ///
    /// <para><strong>Partial Update Support:</strong></para>
    /// - Only specified fields are updated
    /// - Null/empty values are ignored for optional fields
    /// - Condition-specific properties can be updated independently
    /// - Associated topics can be completely replaced or left unchanged
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - Adjusting trigger thresholds and values
    /// - Updating priority and timing settings
    /// - Modifying associated topics
    /// - Changing trigger descriptions and names
    /// - Fine-tuning condition-specific parameters
    /// </remarks>
    /// <param name="id">Unique prompt trigger identifier (GUID format)</param>
    /// <param name="request">Partial update data with only fields to be changed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The updated prompt trigger with new values and updated timestamp</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(PromptTriggerDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdatePromptTrigger(Guid id, [FromBody] UpdatePromptTriggerRequest request, CancellationToken cancellationToken = default)
    {
        var dto = new UpdatePromptTriggerDto
        {
            Name = request.Name,
            Description = request.Description,
            Priority = request.Priority,
            InsightNumber = request.InsightNumber,
            CooldownMinutes = request.CooldownMinutes,
            DailyLimit = request.DailyLimit,
            TriggerCondition = request.TriggerCondition,
            ConditionType = request.ConditionType,
            ScoreType = request.ScoreType,
            ScoreOperator = request.ScoreOperator,
            ScoreValue = request.ScoreValue,
            StartTime = request.StartTime,
            EndTime = request.EndTime,
            MetricType = request.MetricType,
            MetricOperator = request.MetricOperator,
            MetricValue = request.MetricValue,
            AssociatedTopicIds = request.AssociatedTopicIds
        };

        return await ExecuteNotFoundAsync(
            () => _promptTriggerService.UpdatePromptTriggerAsync(id, dto, cancellationToken),
            "Prompt trigger not found",
            "Failed to update prompt trigger"
        );
    }

    /// <summary>
    /// Activate a prompt trigger to enable its execution
    /// </summary>
    /// <remarks>
    /// Activates the specified prompt trigger, enabling it to be evaluated and executed
    /// when its conditions are met. Only active triggers participate in the trigger evaluation process.
    ///
    /// <para><strong>Activation Effects:</strong></para>
    /// - Sets IsActive flag to true
    /// - Updates the UpdatedAt timestamp
    /// - Trigger becomes eligible for condition evaluation
    /// - Trigger will be included in active trigger queries
    ///
    /// <para><strong>Business Rules:</strong></para>
    /// - Trigger must exist to be activated
    /// - Already active triggers can be activated again (idempotent operation)
    /// - Activation does not validate trigger conditions
    /// - Priority determines execution order among active triggers
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// - Enabling newly created triggers
    /// - Re-enabling temporarily disabled triggers
    /// - Batch activation of trigger sets
    /// - Automated trigger management workflows
    /// </remarks>
    /// <param name="id">Unique prompt trigger identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success confirmation with activation status</returns>
    [HttpPost("{id:guid}/activate")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ActivatePromptTrigger(Guid id, CancellationToken cancellationToken = default)
    {
        return await ExecuteNotFoundAsync(
            () => _promptTriggerService.ActivatePromptTriggerAsync(id, cancellationToken),
            "Prompt trigger not found",
            "Failed to activate prompt trigger"
        );
    }

    /// <summary>
    /// Deactivate a prompt trigger to disable its execution
    /// </summary>
    /// <remarks>
    /// Deactivates the specified prompt trigger, preventing it from being evaluated and executed
    /// even when its conditions are met. Deactivated triggers are excluded from trigger evaluation.
    ///
    /// <para><strong>Deactivation Effects:</strong></para>
    /// - Sets IsActive flag to false
    /// - Updates the UpdatedAt timestamp
    /// - Trigger is excluded from condition evaluation
    /// - Trigger will not appear in active trigger queries
    ///
    /// <para><strong>Business Rules:</strong></para>
    /// - Trigger must exist to be deactivated
    /// - Already inactive triggers can be deactivated again (idempotent operation)
    /// - Deactivation preserves all trigger configuration
    /// - Trigger can be reactivated at any time
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// - Temporarily disabling problematic triggers
    /// - Seasonal or conditional trigger management
    /// - Testing and debugging trigger configurations
    /// - Maintenance and system updates
    /// - Gradual rollout of trigger changes
    /// </remarks>
    /// <param name="id">Unique prompt trigger identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success confirmation with deactivation status</returns>
    [HttpPost("{id:guid}/deactivate")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeactivatePromptTrigger(Guid id, CancellationToken cancellationToken = default)
    {
        return await ExecuteNotFoundAsync(
            () => _promptTriggerService.DeactivatePromptTriggerAsync(id, cancellationToken),
            "Prompt trigger not found",
            "Failed to deactivate prompt trigger"
        );
    }

    /// <summary>
    /// Permanently delete a prompt trigger
    /// </summary>
    /// <remarks>
    /// Permanently removes the specified prompt trigger from the system. This operation cannot be undone
    /// and will immediately stop all trigger evaluations for the deleted trigger.
    ///
    /// <para><strong>Deletion Effects:</strong></para>
    /// - Trigger is permanently removed from the database
    /// - All trigger configuration and history is lost
    /// - Trigger immediately stops participating in evaluations
    /// - Associated topic relationships are removed
    ///
    /// <para><strong>Business Rules:</strong></para>
    /// - Trigger must exist to be deleted
    /// - Deletion is immediate and irreversible
    /// - No cascade deletion of related entities
    /// - Audit logs may retain deletion records
    ///
    /// <para><strong>Safety Considerations:</strong></para>
    /// - Consider deactivating instead of deleting for temporary removal
    /// - Ensure trigger is not critical to active workflows
    /// - Backup trigger configuration before deletion if needed
    /// - Verify no dependent systems rely on this trigger
    ///
    /// <para><strong>Use Cases:</strong></para>
    /// - Removing obsolete or incorrect triggers
    /// - Cleaning up test and development triggers
    /// - System maintenance and optimization
    /// - Compliance with data retention policies
    /// </remarks>
    /// <param name="id">Unique prompt trigger identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success confirmation of deletion</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeletePromptTrigger(Guid id, CancellationToken cancellationToken = default)
    {
        return await ExecuteNotFoundAsync(
            () => _promptTriggerService.DeletePromptTriggerAsync(id, cancellationToken),
            "Prompt trigger not found",
            "Failed to delete prompt trigger"
        );
    }
}
