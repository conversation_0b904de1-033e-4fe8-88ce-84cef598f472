using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Controllers;
using Rolla.Modules.RollaAdmin.Api.Contracts.Prompts;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Prompts;
using Microsoft.AspNetCore.Mvc;
using Rolla.BuildingBlocks.Application.Common;

namespace Rolla.Modules.RollaAdmin.Api.Controllers;

/// <summary>
/// Manages AI prompts with comprehensive CRUD operations, search, filtering, and activation features.
/// </summary>
[ApiController]
[Route("api/rolla-admin/prompts")]
[Produces("application/json")]
[Tags("Rolla Admin - Prompt Management System - Prompts")]
public class PromptController : BaseController
{
    private readonly IPromptService _promptService;

    public PromptController(ILogger<PromptController> logger, IPromptService promptService)
        : base(logger)
    {
        _promptService = promptService;
    }

    /// <summary>
    /// Get all prompts with advanced filtering, search, and pagination
    /// </summary>
    /// <remarks>
    /// Retrieve prompts with powerful search and filtering capabilities:
    ///
    /// <para><strong>Pagination Features:</strong></para>
    /// - Configurable page sizes (1-100 items)
    /// - Total count and navigation metadata
    /// - Consistent results with secondary sorting
    ///
    /// <para><strong>Search and Filter Options: </strong></para>
    /// - Full-text search across name, description, and content
    /// - Filter by active status and date ranges
    /// - Combine multiple filters for precise results
    ///
    /// <para><strong>Sorting Options:</strong></para>
    /// - Sort by: Name, Description, IsActive, CreatedAt, UpdatedAt
    /// - Choose ascending or descending order
    /// - Default: newest first (CreatedAt desc)
    /// </remarks>
    /// <param name="searchTerm">Search across name, description, or content (max 100 characters)</param>
    /// <param name="isActive">Filter by status: true (active only), false (inactive only), null (all)</param>
    /// <param name="createdAfter">Show prompts created after this date (ISO 8601)</param>
    /// <param name="createdBefore">Show prompts created before this date (ISO 8601)</param>
    /// <param name="updatedAfter">Show prompts updated after this date (ISO 8601)</param>
    /// <param name="updatedBefore">Show prompts updated before this date (ISO 8601)</param>
    /// <param name="sortBy">Sort field: Name, Description, IsActive, CreatedAt, UpdatedAt (default: CreatedAt)</param>
    /// <param name="sortDescending">Sort direction: true for newest first, false for oldest first (default: true)</param>
    /// <param name="pageNumber">Page number starting from 1 (default: 1)</param>
    /// <param name="pageSize">Items per page, max 100 (default: 10)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of prompts with metadata</returns>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResult<PromptDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetPrompts(
        [FromQuery] string? searchTerm,
        [FromQuery] bool? isActive,
        [FromQuery] DateTime? createdAfter,
        [FromQuery] DateTime? createdBefore,
        [FromQuery] DateTime? updatedAfter,
        [FromQuery] DateTime? updatedBefore,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] bool sortDescending = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var request = new PromptFilterRequest
        {
            SearchTerm = searchTerm,
            IsActive = isActive,
            CreatedAfter = createdAfter,
            CreatedBefore = createdBefore,
            UpdatedAfter = updatedAfter,
            UpdatedBefore = updatedBefore,
            SortBy = sortBy,
            SortDescending = sortDescending,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        return await ExecuteAsync(
            () => _promptService.GetAllPromptsAsync(request, cancellationToken),
            "Prompts retrieved successfully",
            "Failed to retrieve prompts"
        );
    }

    /// <summary>
    /// Get a specific prompt by ID
    /// </summary>
    /// <remarks>
    /// Retrieve detailed information about a single prompt including all properties and metadata.
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - View prompt details in admin interfaces
    /// - Load prompt for editing
    /// - Retrieve content for AI processing
    /// - Audit and compliance reporting
    ///
    /// <para><strong>Security Notes:</strong></para>
    /// - Requires valid authentication
    /// - GUID validation prevents injection attacks
    /// - Returns 404 for non-existent prompts
    /// </remarks>
    /// <param name="id">Unique prompt identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Complete prompt details</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(PromptDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetPrompt(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteNotFoundAsync(
            () => _promptService.GetPromptByIdAsync(id, cancellationToken),
            "Prompt not found",
            $"Failed to retrieve prompt {id}"
        );
    }

    /// <summary>
    /// Get the currently active prompt
    /// </summary>
    /// <remarks>
    /// Retrieve the prompt currently marked as active in the system. Only one prompt can be active at a time.
    ///
    /// <para><strong>Key Information:</strong></para>
    /// - Only one active prompt exists at any time
    /// - Returns 404 if no prompt is currently active
    /// - Active status managed via the activate endpoint
    ///
    /// <para><strong>Typical Usage:</strong></para>:
    /// - AI systems fetching current prompt for processing
    /// - Admin dashboards showing active configuration
    /// - Health checks and monitoring
    /// </remarks>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The currently active prompt</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(PromptDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetActivePrompt(CancellationToken cancellationToken)
    {
        return await ExecuteNotFoundAsync(
            () => _promptService.GetActivePromptAsync(cancellationToken),
            "No active prompt found",
            "Failed to retrieve active prompt"
        );
    }

    /// <summary>
    /// Create a new prompt
    /// </summary>
    /// <remarks>
    /// Create a new prompt in the system. New prompts start in inactive state and must be explicitly activated.
    ///
    /// <para><strong>Required Fields:</strong></para>
    /// - Name: Must be unique across all prompts
    /// - SystemPromptContent: The actual prompt text
    ///
    /// <para><strong>Optional Fields:</strong></para>
    /// - Description: Recommended for documentation
    ///
    /// <para><strong>Important Notes:</strong></para>
    /// - New prompts are inactive by default
    /// - Names must be unique (case-insensitive)
    /// - Creation timestamp set automatically
    /// - Ready for testing immediately after creation
    /// </remarks>
    /// <param name="request">Prompt creation details (name, description, content)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The newly created prompt with ID and timestamps</returns>
    [HttpPost]
    [ProducesResponseType(typeof(PromptDto), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreatePrompt([FromBody] CreatePromptRequestComposition request, CancellationToken cancellationToken)
    {
        var dto = new CreatePromptDto
        {
            Name = request.Name,
            Description = request.Description,
            SystemPromptContent = request.SystemPromptContent,
        };

        return await ExecuteCreatedAsync(
            () => _promptService.CreatePromptAsync(dto, cancellationToken),
            "Prompt created successfully",
            "Failed to create prompt"
        );
    }

    /// <summary>
    /// Update an existing prompt
    /// </summary>
    /// <remarks>
    /// Update any field of an existing prompt. Changes are applied atomically with automatic timestamp updates.
    ///
    /// <para><strong>Update Behavior:</strong></para>
    /// - Only provided fields are updated (partial updates supported)
    /// - UpdatedAt timestamp set automatically
    /// - Active status preserved unless explicitly changed
    /// - All changes are atomic and thread-safe
    ///
    /// <para><strong>Validation Rules:</strong></para>
    /// - Names must remain unique (excluding current prompt)
    /// - SystemPromptContent cannot be empty if provided
    /// - All text fields validated for length and content
    ///
    /// <para><strong>Concurrency:</strong></para>
    /// - Thread-safe operations
    /// - Last-write-wins for concurrent updates
    /// </remarks>
    /// <param name="id">Unique prompt identifier (GUID format)</param>
    /// <param name="request">Fields to update (partial updates supported)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated prompt with new timestamp</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(PromptDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdatePrompt(Guid id, [FromBody] UpdatePromptRequestComposition request, CancellationToken cancellationToken)
    {
        var dto = new UpdatePromptDto
        {
            Name = request.Name,
            Description = request.Description,
            SystemPromptContent = request.SystemPromptContent
        };

        return await ExecuteAsync(
            () => _promptService.UpdatePromptAsync(id, dto, cancellationToken),
            "Prompt updated successfully",
            $"Failed to update prompt {id}"
        );
    }

    /// <summary>
    /// Activate a prompt (make it the current active prompt)
    /// </summary>
    /// <remarks>
    /// Set the specified prompt as active and automatically deactivate any previously active prompt.
    ///
    /// <para><strong>Activation Rules:</strong></para>
    /// - Only one prompt can be active at any time
    /// - Previous active prompt automatically deactivated
    /// - Operation is atomic (all changes succeed or none do)
    /// - UpdatedAt timestamp set for both prompts
    ///
    /// <para><strong>Common Use Case:</strong></para>:
    /// - Deploy new prompt versions to production
    /// - Roll back to previous prompt versions
    /// - A/B testing different configurations
    /// - Emergency prompt changes
    /// </remarks>
    /// <param name="id">Unique prompt identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Confirmation of successful activation</returns>
    [HttpPost("{id:guid}/activate")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ActivatePrompt(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            () => _promptService.ActivatePromptAsync(id, cancellationToken),
            "Prompt activated successfully",
            $"Failed to activate prompt {id}"
        );
    }

    /// <summary>
    /// Deactivate a prompt (remove active status)
    /// </summary>
    /// <remarks>
    /// Deactivate the specified prompt, removing its active status. This allows other prompts to be activated.
    ///
    /// <para><strong>Deactivation Rules:</strong></para>
    /// - Sets IsActive flag to false
    /// - UpdatedAt timestamp updated automatically
    /// - Operation is atomic and thread-safe
    /// - Can be reactivated at any time
    ///
    /// <para><strong>Common Use Cases:</strong></para>:
    /// - Temporarily disable a prompt
    /// - Prepare for activating a different prompt
    /// - Emergency prompt deactivation
    /// - Testing and maintenance scenarios
    /// </remarks>
    /// <param name="id">Unique prompt identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Confirmation of successful deactivation</returns>
    [HttpPost("{id:guid}/deactivate")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeactivatePrompt(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            () => _promptService.DeactivatePromptAsync(id, cancellationToken),
            "Prompt deactivated successfully",
            $"Failed to deactivate prompt {id}"
        );
    }

    /// <summary>
    /// Delete a prompt permanently
    /// </summary>
    /// <remarks>
    /// WARNING: This action cannot be undone!
    ///
    /// Permanently remove a prompt from the system with all references cleaned up.
    ///
    /// <para><strong>Safety Restrictions:</strong></para>
    /// - Cannot delete currently active prompts (deactivate first)
    /// - Prompt must exist and be accessible
    /// - All system references are automatically cleaned up
    ///
    /// <para><strong>Best Practices:</strong></para>
    /// - Consider deactivating instead of deleting production prompts
    /// - Back up important prompts before deletion
    /// - Verify no active processes are using the prompt
    /// - Document deletion reasons in external systems
    ///
    /// <para><strong>Typical Use Cases:</strong></para>
    /// - Remove test or development prompts
    /// - Clean up obsolete prompt versions
    /// - Remove prompts with security issues
    /// - System maintenance and cleanup
    /// </remarks>
    /// <param name="id">Unique prompt identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content on successful deletion</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeletePrompt(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteNoContentAsync(
            () => _promptService.DeletePromptAsync(id, cancellationToken),
            "Prompt deleted successfully",
            $"Failed to delete prompt {id}"
        );
    }
}
