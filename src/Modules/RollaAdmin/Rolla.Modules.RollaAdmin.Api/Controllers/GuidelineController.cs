using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Controllers;
using Rolla.Modules.RollaAdmin.Api.Contracts.Guidelines;
using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Guidelines;

namespace Rolla.Modules.RollaAdmin.Api.Controllers;

/// <summary>
/// Manages AI guidelines with comprehensive CRUD operations, search, filtering, and activation features.
/// </summary>
[ApiController]
[Route("api/rolla-admin/guidelines")]
[Produces("application/json")]
[Tags("Rolla Admin - Prompt Management System - Guidelines")]
public class GuidelineController(ILogger<GuidelineController> logger, IGuidelineService guidelineService): BaseController(logger)
{
    private readonly IGuidelineService _guidelineService = guidelineService;

    /// <summary>
    /// Get all guidelines with advanced filtering, search, and pagination
    /// </summary>
    /// <remarks>
    /// Retrieve guidelines with powerful search and filtering capabilities:
    ///
    /// <para><strong>Pagination Features:</strong></para>
    /// - Configurable page sizes (1-100 items)
    /// - Total count and navigation metadata
    /// - Consistent results with secondary sorting
    ///
    /// <para><strong>Search and Filter Options: </strong></para>
    /// - Full-text search across name, description, and content
    /// - Filter by active status and date ranges
    /// - Combine multiple filters for precise results
    ///
    /// <para><strong>Sorting Options:</strong></para>
    /// - Sort by: Name, Description, IsActive, CreatedAt, UpdatedAt
    /// - Choose ascending or descending order
    /// - Default: newest first (CreatedAt desc)
    /// </remarks>
    /// <param name="searchTerm">Search across name, description, or content (max 100 characters)</param>
    /// <param name="isActive">Filter by status: true (active only), false (inactive only), null (all)</param>
    /// <param name="createdAfter">Show guidelines created after this date (ISO 8601)</param>
    /// <param name="createdBefore">Show guidelines created before this date (ISO 8601)</param>
    /// <param name="updatedAfter">Show guidelines updated after this date (ISO 8601)</param>
    /// <param name="updatedBefore">Show guidelines updated before this date (ISO 8601)</param>
    /// <param name="sortBy">Sort field: Name, Description, IsActive, CreatedAt, UpdatedAt (default: CreatedAt)</param>
    /// <param name="sortDescending">Sort direction: true for newest first, false for oldest first (default: true)</param>
    /// <param name="pageNumber">Page number starting from 1 (default: 1)</param>
    /// <param name="pageSize">Items per page, max 100 (default: 10)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of guidelines with metadata</returns>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResult<GuidelineDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetGuidelines(
        [FromQuery] string? searchTerm,
        [FromQuery] bool? isActive,
        [FromQuery] DateTime? createdAfter,
        [FromQuery] DateTime? createdBefore,
        [FromQuery] DateTime? updatedAfter,
        [FromQuery] DateTime? updatedBefore,
        [FromQuery] string? sortBy = "CreatedAt",
        [FromQuery] bool sortDescending = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var request = new GuidelineFilterRequest
        {
            SearchTerm = searchTerm,
            IsActive = isActive,
            CreatedAfter = createdAfter,
            CreatedBefore = createdBefore,
            UpdatedAfter = updatedAfter,
            UpdatedBefore = updatedBefore,
            SortBy = sortBy,
            SortDescending = sortDescending,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        return await ExecuteAsync(
            ()=> _guidelineService.GetAllGuidelinesAsync(request, cancellationToken),
            "Guidelines retrieved successfully",
            "Failed to retrieve guidelines"
        );
    }

    /// <summary>
    /// Get a specific guideline by ID
    /// </summary>
    /// <remarks>
    /// Retrieve detailed information about a single guideline including all properties and metadata.
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - View guideline details in admin interfaces
    /// - Load guideline for editing
    /// - Retrieve content for AI processing
    /// - Audit and compliance reporting
    ///
    /// <para><strong>Security Notes:</strong></para>
    /// - Requires valid authentication
    /// - GUID validation prevents injection attacks
    /// - Returns 404 for non-existent guidelines
    /// </remarks>
    /// <param name="id">Unique guideline identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Complete guideline details</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(GuidelineDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetGuideline(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteNotFoundAsync(
             () => _guidelineService.GetGuidelineByIdAsync(id, cancellationToken),
            "Guideline not found",
            $"Failed to retrieve guideline {id}"
        );
    }

    /// <summary>
    /// Get the currently active guideline
    /// </summary>
    /// <remarks>
    /// Retrieve the guideline currently marked as active in the system. Only one guideline can be active at a time.
    ///
    /// <para><strong>Key Information:</strong></para>
    /// - Only one active guideline exists at any time
    /// - Returns 404 if no guideline is currently active
    /// - Active status managed via the activate endpoint
    ///
    /// <para><strong>Typical Usage:</strong></para>:
    /// - AI systems fetching current guideline for processing
    /// - Admin dashboards showing active configuration
    /// - Health checks and monitoring
    /// </remarks>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The currently active guideline</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(GuidelineDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetActiveGuideline(CancellationToken cancellationToken)
    {
        return await ExecuteNotFoundAsync(
             () => _guidelineService.GetActiveGuidelineAsync(cancellationToken),
            "No active guideline found",
            "Failed to retrieve active guideline"
        );
    }

    /// <summary>
    /// Create a new guideline
    /// </summary>
    /// <remarks>
    /// Create a new guideline in the system. New guidelines start in inactive state and must be explicitly activated.
    ///
    /// <para><strong>Required Fields:</strong></para>
    /// - Name: Must be unique across all guidelines
    /// - PromptGuidelineContent: The actual guideline text
    ///
    /// <para><strong>Optional Fields:</strong></para>
    /// - Description: Recommended for documentation
    ///
    /// <para><strong>Important Notes:</strong></para>
    /// - New guidelines are inactive by default
    /// - Names must be unique (case-insensitive)
    /// - Creation timestamp set automatically
    /// - Ready for testing immediately after creation
    /// </remarks>
    /// <param name="request">Guideline creation details (name, description, content)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The newly created guideline with ID and timestamps</returns>
    [HttpPost]
    [ProducesResponseType(typeof(GuidelineDto), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateGuideline([FromBody] CreateGuidelineRequestComposition request, CancellationToken cancellationToken)
    {
        var dto = new CreateGuidelineDto
        {
            Name = request.Name,
            Description = request.Description,
            PromptGuidelineContent = request.PromptGuidelineContent,
        };

        return await ExecuteCreatedAsync(
             () => _guidelineService.CreateGuidelineAsync(dto, cancellationToken),
            "Guideline created successfully",
            "Failed to create guideline"
        );
    }

    /// <summary>
    /// Update an existing guideline
    /// </summary>
    /// <remarks>
    /// Update any field of an existing guideline. The operation is atomic and maintains data consistency.
    ///
    /// <para><strong>Update Behavior:</strong></para>
    /// - Only provided fields are updated (partial updates supported)
    /// - Null values mean "no change" for all fields
    /// - Empty strings are treated as valid updates (will clear optional fields)
    /// - UpdatedAt timestamp set automatically
    /// - Active status preserved unless explicitly changed
    ///
    /// <para><strong>Field Update Rules:</strong></para>
    /// - Name: null = no change, empty string = not allowed (validation error)
    /// - Description: null = no change, empty string = clears the field
    /// - Content: null = no change, empty string = not allowed (validation error)
    /// - At least one field must be provided for update
    ///
    /// <para><strong>Validation:</strong></para>
    /// - Name must be unique (excluding current guideline) and max 100 characters
    /// - Description max 500 characters when provided
    /// - Content max 5000 characters when provided
    /// - Guideline must exist and be accessible
    ///
    /// <para><strong>Important Notes:</strong></para>
    /// - Updates are atomic and thread-safe
    /// - Changes immediately visible in subsequent requests
    /// - If updating active guideline, cache invalidated automatically
    /// </remarks>
    /// <param name="id">Unique guideline identifier (GUID format)</param>
    /// <param name="request">Update details (name, description, content)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The updated guideline with new timestamp</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(GuidelineDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateGuideline(Guid id, [FromBody] UpdateGuidelineRequestComposition request, CancellationToken cancellationToken)
    {
        var dto = new UpdateGuidelineDto
        {
            Name = request.Name,
            Description = request.Description,
            PromptGuidelineContent = request.PromptGuidelineContent,
        };

        return await ExecuteAsync(
            ()=> _guidelineService.UpdateGuidelineAsync(id, dto, cancellationToken),
            "Guideline updated successfully",
            "Failed to update guideline"
        );
    }

    /// <summary>
    /// Activate a specific guideline
    /// </summary>
    /// <remarks>
    /// Set the specified guideline as active and automatically deactivate any previously active guideline.
    /// Only one guideline can be active at any time.
    ///
    /// <para><strong>Key Behavior:</strong></para>
    /// - Only one guideline active at any time
    /// - Activating one automatically deactivates others
    /// - Operation is atomic (all changes succeed or none do)
    /// - UpdatedAt timestamp set for affected guidelines
    ///
    /// <para><strong>System Impact:</strong></para>
    /// - AI systems immediately use newly activated guideline
    /// - Active guideline cache invalidated and refreshed
    /// - All dependent systems notified of change
    ///
    /// <para><strong>Common Use Cases:</strong></para>
    /// - Deploy new guideline versions to production
    /// - Roll back to previous guideline versions
    /// - A/B test different guideline configurations
    /// - Emergency guideline changes
    /// </remarks>
    /// <param name="id">Unique guideline identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Confirmation of successful activation</returns>
    [HttpPost("{id:guid}/activate")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> ActivateGuideline(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            ()=> _guidelineService.ActivateGuidelineAsync(id, cancellationToken),
            "Guideline activated successfully",
            "Failed to activate guideline"
        );
    }

    /// <summary>
    /// Delete a guideline permanently
    /// </summary>
    /// <remarks>
    /// Permanently remove a guideline from the system. **This operation cannot be undone.**
    ///
    /// <para><strong>⚠️ WARNING: Irreversible Operation!</strong></para>
    ///
    /// <para><strong>Safety Measures:</strong></para>
    /// - Guideline must exist and be accessible
    /// - Operation logged for audit purposes
    /// - All references cleaned up automatically
    ///
    /// <para><strong>System Impact:</strong></para>
    /// - If deleting active guideline, cache invalidated
    /// - Historical references may become invalid
    /// - Audit logs preserve deletion event
    ///
    /// <para><strong>Best Practices:</strong></para>
    /// - Consider deactivating instead of deleting production guidelines
    /// - Backup important guidelines before deletion
    /// - Verify no active processes using the guideline
    /// - Document deletion reason in external systems
    ///
    /// <para><strong>Typical Use Cases:</strong></para>
    /// - Remove test/development guidelines
    /// - Clean up obsolete guideline versions
    /// - Remove guidelines with security issues
    /// - System maintenance and cleanup
    /// </remarks>
    /// <param name="id">Unique guideline identifier (GUID format)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content on successful deletion</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteGuideline(Guid id, CancellationToken cancellationToken)
    {
        return await ExecuteNoContentAsync(
            () => _guidelineService.DeleteGuidelineAsync(id, cancellationToken),
            "Guideline deleted successfully",
            "Failed to delete guideline"
        );
    }
}
