namespace Rolla.Modules.RollaAdmin.Application.Constants.PromptTriggers;

public static class ValidationConstants
{
    public const int NameMaxLength = 100;
    public const int DescriptionMaxLength = 500;
    public const int ValueMaxLength = 50;
    public const int PriorityMinValue = 1;
    public const int PriorityMaxValue = 1000;
    public const int InsightNumberMinValue = 1;
    public const int InsightNumberMaxValue = 1000;
    public const int CooldownMinutesMinValue = 0;
    public const int CooldownMinutesMaxValue = 10080;
    public const int DailyLimitMinValue = 1;
    public const int DailyLimitMaxValue = 100;
}
