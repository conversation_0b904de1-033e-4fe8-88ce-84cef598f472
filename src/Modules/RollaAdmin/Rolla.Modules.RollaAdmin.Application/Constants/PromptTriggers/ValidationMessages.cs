namespace Rolla.Modules.RollaAdmin.Application.Constants.PromptTriggers;

public static class ValidationMessages
{
    public const string NameRequired = "Name is required.";
    public const string NameMaxLength = "Name cannot exceed 100 characters.";
    public const string DescriptionMaxLength = "Description cannot exceed 500 characters.";
    public const string PriorityRange = "Priority must be between 1 and 1000.";
    public const string InsightNumberRange = "Insight number must be between 1 and 1000.";
    public const string CooldownMinutesRange = "Cooldown minutes must be between 0 and 10080 (7 days).";
    public const string DailyLimitRange = "Daily limit must be between 1 and 100.";
    public const string TriggerConditionRequired = "Trigger condition is required.";
    public const string ConditionTypeRequired = "Condition type is required.";
    public const string ScoreTypeRequired = "Score type is required when condition type is ScoreValue.";
    public const string OperatorRequired = "Operator is required when condition type is ScoreValue or MetricValue.";
    public const string ValueRequired = "Value is required when operator is not ChangedDuringDay.";
    public const string ValueNotAllowed = "Value must be null when operator is ChangedDuringDay.";
    public const string ValueMaxLength = "Value cannot exceed 50 characters.";
    public const string StartTimeRequired = "Start time is required when condition type is TimeOfDay.";
    public const string EndTimeRequired = "End time is required when condition type is TimeOfDay.";
    public const string StartTimeBeforeEndTime = "Start time must be before end time.";
    public const string MetricTypeRequired = "Metric type is required when condition type is MetricValue.";
    public const string HaveAtLeastOneField = "At least one field must be provided for update.";
}
