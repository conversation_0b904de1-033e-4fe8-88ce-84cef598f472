namespace Rolla.Modules.RollaAdmin.Application.Constants.Topics;

public static class ValidationMessages
{
    public const string NameRequired = "Name is required.";
    public const string NameMaxLength = "Name cannot exceed 100 characters.";
    public const string DescriptionMaxLength = "Description cannot exceed 500 characters.";
    public const string PromptRequired = "Prompt is required.";
    public const string PromptMaxLength = "Prompt cannot exceed 5000 characters.";
    public const string PriorityRange = "Priority must be between 0 and 1000.";
    public const string HaveAtLeastOneField = "At least one field (Name, Description, Prompt, or Priority) must be provided for update";
}
