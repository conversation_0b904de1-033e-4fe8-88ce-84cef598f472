namespace Rolla.Modules.RollaAdmin.Application.Constants.Prompts;

public static class ValidationMessages
{
    public const string NameRequired = "Name is required.";
    public const string NameMaxLength = "Name cannot exceed 100 characters.";
    public const string DescriptionMaxLength = "Description cannot exceed 500 characters.";
    public const string SystemPromptRequired = "System prompt content is required.";
    public const string SystemPromptMaxLength = "System prompt content cannot exceed 5000 characters.";
    public const string HaveAtLeastOneField = "At least one field (Name, SystemPromptContent, or Description) must be provided for update";
}
