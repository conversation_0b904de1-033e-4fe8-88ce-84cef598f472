namespace Rolla.Modules.RollaAdmin.Application.Configuration;

public class PromptSettings
{
    public const string SectionName = "PromptSettings";
    
    public PaginationSettings Pagination { get; set; } = new();
    public CacheSettings Cache { get; set; } = new();
    public SearchSettings Search { get; set; } = new();
}

public class PaginationSettings
{
    public int DefaultPageSize { get; set; } = 10;
    public int MaxPageSize { get; set; } = 100;
    public int MinPageSize { get; set; } = 1;
}

public class CacheSettings
{
    public int ActivePromptCacheMinutes { get; set; } = 30;
    public bool EnableCaching { get; set; } = true;
}

public class SearchSettings
{
    public int MaxSearchTermLength { get; set; } = 100;
    public bool EnableFullTextSearch { get; set; } = false;
    public string[] SearchableFields { get; set; } = { "Name", "Description", "SystemPromptContent" };
}
