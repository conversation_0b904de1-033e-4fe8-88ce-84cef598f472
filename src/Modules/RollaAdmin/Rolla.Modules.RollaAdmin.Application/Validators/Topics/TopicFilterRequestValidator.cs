using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Topics;

public class TopicFilterRequestValidator : AbstractValidator<TopicFilterRequest>
{
    public TopicFilterRequestValidator()
    {
        RuleFor(x => x.PageNumber)
            .GreaterThan(0).WithMessage("Page number must be greater than 0.");

        RuleFor(x => x.PageSize)
            .InclusiveBetween(1, 100).WithMessage("Page size must be between 1 and 100.");

        RuleFor(x => x.SearchTerm)
            .MaximumLength(100).WithMessage("Search term cannot exceed 100 characters.")
            .When(x => !string.IsNullOrWhiteSpace(x.SearchTerm));

        RuleFor(x => x.MinPriority)
            .GreaterThanOrEqualTo(0).WithMessage("Minimum priority must be non-negative.")
            .When(x => x.MinPriority.HasValue);

        RuleFor(x => x.MaxPriority)
            .GreaterThanOrEqualTo(0).WithMessage("Maximum priority must be non-negative.")
            .When(x => x.MaxPriority.HasValue);

        RuleFor(x => x)
            .Must(x => !x.MinPriority.HasValue || !x.MaxPriority.HasValue || x.MinPriority <= x.MaxPriority)
            .WithMessage("Minimum priority must be less than or equal to maximum priority.")
            .When(x => x.MinPriority.HasValue && x.MaxPriority.HasValue);

        RuleFor(x => x.CreatedAfter)
            .LessThanOrEqualTo(DateTime.UtcNow).WithMessage("Created after date cannot be in the future.")
            .When(x => x.CreatedAfter.HasValue);

        RuleFor(x => x.CreatedBefore)
            .LessThanOrEqualTo(DateTime.UtcNow).WithMessage("Created before date cannot be in the future.")
            .When(x => x.CreatedBefore.HasValue);

        RuleFor(x => x)
            .Must(x => !x.CreatedAfter.HasValue || !x.CreatedBefore.HasValue || x.CreatedAfter <= x.CreatedBefore)
            .WithMessage("Created after date must be before created before date.")
            .When(x => x.CreatedAfter.HasValue && x.CreatedBefore.HasValue);
    }
}
