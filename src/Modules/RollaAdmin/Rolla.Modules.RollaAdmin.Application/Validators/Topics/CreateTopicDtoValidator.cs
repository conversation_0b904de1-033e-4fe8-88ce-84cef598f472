using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.Topics;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Topics;

public class CreateTopicDtoValidator : AbstractValidator<CreateTopicDto>
{
    public CreateTopicDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage(ValidationMessages.NameRequired)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength);

        RuleFor(x => x.Prompt)
            .NotEmpty().WithMessage(ValidationMessages.PromptRequired)
            .MaximumLength(ValidationConstants.PromptMaxLength).WithMessage(ValidationMessages.PromptMaxLength);

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength).WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Description));

        RuleFor(x => x.Priority)
            .InclusiveBetween(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue)
            .WithMessage(ValidationMessages.PriorityRange);
    }
}
