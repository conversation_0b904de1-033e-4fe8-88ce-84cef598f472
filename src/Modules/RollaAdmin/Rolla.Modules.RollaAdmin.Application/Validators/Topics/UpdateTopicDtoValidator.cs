using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.Topics;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Topics;

public class UpdateTopicDtoValidator : AbstractValidator<UpdateTopicDto>
{
    public UpdateTopicDtoValidator()
    {
        RuleFor(x => x)
            .Must(HaveAtLeastOneField)
            .WithMessage(ValidationMessages.HaveAtLeastOneField);

        RuleFor(x => x.Name)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Name));

        RuleFor(x => x.Prompt)
            .MaximumLength(ValidationConstants.PromptMaxLength).WithMessage(ValidationMessages.PromptMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Prompt));

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength).WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Description));

        RuleFor(x => x.Priority)
            .InclusiveBetween(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue)
            .WithMessage(ValidationMessages.PriorityRange)
            .When(x => x.Priority.HasValue);
    }

    private static bool HaveAtLeastOneField(UpdateTopicDto dto)
    {
        return !string.IsNullOrWhiteSpace(dto.Name) ||
               !string.IsNullOrWhiteSpace(dto.Prompt) ||
               dto.Description != null ||
               dto.Priority.HasValue;
    }
}
