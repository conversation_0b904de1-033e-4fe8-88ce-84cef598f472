using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Application.Validators.PromptTriggers;

public class CreatePromptTriggerDtoValidator : AbstractValidator<CreatePromptTriggerDto>
{
    public CreatePromptTriggerDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage(ValidationMessages.NameRequired)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength);

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength).WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.Priority)
            .InclusiveBetween(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue)
            .WithMessage(ValidationMessages.PriorityRange);

        RuleFor(x => x.InsightNumber)
            .InclusiveBetween(ValidationConstants.InsightNumberMinValue, ValidationConstants.InsightNumberMaxValue)
            .WithMessage(ValidationMessages.InsightNumberRange);

        RuleFor(x => x.CooldownMinutes)
            .InclusiveBetween(ValidationConstants.CooldownMinutesMinValue, ValidationConstants.CooldownMinutesMaxValue)
            .WithMessage(ValidationMessages.CooldownMinutesRange);

        RuleFor(x => x.DailyLimit)
            .InclusiveBetween(ValidationConstants.DailyLimitMinValue, ValidationConstants.DailyLimitMaxValue)
            .WithMessage(ValidationMessages.DailyLimitRange);

        RuleFor(x => x.TriggerCondition)
            .IsInEnum().WithMessage(ValidationMessages.TriggerConditionRequired);

        RuleFor(x => x.ConditionType)
            .IsInEnum().WithMessage(ValidationMessages.ConditionTypeRequired);

        // Score Value specific validations
        When(x => x.ConditionType == ConditionType.ScoreValue, () =>
        {
            RuleFor(x => x.ScoreType)
                .NotNull().WithMessage(ValidationMessages.ScoreTypeRequired);

            RuleFor(x => x.ScoreOperator)
                .NotNull().WithMessage(ValidationMessages.OperatorRequired);

            RuleFor(x => x.ScoreValue)
                .NotEmpty().WithMessage(ValidationMessages.ValueRequired)
                .When(x => x.ScoreOperator != OperatorType.ChangedDuringDay);

            RuleFor(x => x.ScoreValue)
                .Empty().WithMessage(ValidationMessages.ValueNotAllowed)
                .When(x => x.ScoreOperator == OperatorType.ChangedDuringDay);

            RuleFor(x => x.ScoreValue)
                .MaximumLength(ValidationConstants.ValueMaxLength).WithMessage(ValidationMessages.ValueMaxLength)
                .When(x => !string.IsNullOrEmpty(x.ScoreValue));
        });

        // Time of Day specific validations
        When(x => x.ConditionType == ConditionType.TimeOfDay, () =>
        {
            RuleFor(x => x.StartTime)
                .NotNull().WithMessage(ValidationMessages.StartTimeRequired);

            RuleFor(x => x.EndTime)
                .NotNull().WithMessage(ValidationMessages.EndTimeRequired);

            RuleFor(x => x)
                .Must(x => x.StartTime < x.EndTime)
                .WithMessage(ValidationMessages.StartTimeBeforeEndTime)
                .When(x => x.StartTime.HasValue && x.EndTime.HasValue);
        });

        // Metric Value specific validations
        When(x => x.ConditionType == ConditionType.MetricValue, () =>
        {
            RuleFor(x => x.MetricType)
                .NotNull().WithMessage(ValidationMessages.MetricTypeRequired);

            RuleFor(x => x.MetricOperator)
                .NotNull().WithMessage(ValidationMessages.OperatorRequired);

            RuleFor(x => x.MetricValue)
                .NotEmpty().WithMessage(ValidationMessages.ValueRequired)
                .When(x => x.MetricOperator != OperatorType.ChangedDuringDay);

            RuleFor(x => x.MetricValue)
                .Empty().WithMessage(ValidationMessages.ValueNotAllowed)
                .When(x => x.MetricOperator == OperatorType.ChangedDuringDay);

            RuleFor(x => x.MetricValue)
                .MaximumLength(ValidationConstants.ValueMaxLength).WithMessage(ValidationMessages.ValueMaxLength)
                .When(x => !string.IsNullOrEmpty(x.MetricValue));
        });
    }
}
