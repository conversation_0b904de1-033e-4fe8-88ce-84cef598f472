using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;

namespace Rolla.Modules.RollaAdmin.Application.Validators.PromptTriggers;

public class PromptTriggerFilterRequestValidator : AbstractValidator<PromptTriggerFilterRequest>
{
    private static readonly string[] AllowedSortFields = 
    {
        "Name", "Description", "Priority", "InsightNumber", "CooldownMinutes", 
        "DailyLimit", "IsActive", "TriggerCondition", "ConditionType", 
        "CreatedAt", "UpdatedAt"
    };

    public PromptTriggerFilterRequestValidator()
    {
        RuleFor(x => x.PageNumber)
            .GreaterThan(0).WithMessage("Page number must be greater than 0");

        RuleFor(x => x.PageSize)
            .InclusiveBetween(1, 100).WithMessage("Page size must be between 1 and 100");

        RuleFor(x => x.SearchTerm)
            .MaximumLength(100).WithMessage("Search term cannot exceed 100 characters")
            .When(x => !string.IsNullOrEmpty(x.SearchTerm));

        RuleFor(x => x.MinPriority)
            .GreaterThanOrEqualTo(0).WithMessage("Minimum priority must be non-negative")
            .When(x => x.MinPriority.HasValue);

        RuleFor(x => x.MaxPriority)
            .GreaterThan(x => x.MinPriority).WithMessage("Maximum priority must be greater than minimum priority")
            .When(x => x.MinPriority.HasValue && x.MaxPriority.HasValue);

        RuleFor(x => x.InsightNumber)
            .GreaterThan(0).WithMessage("Insight number must be greater than 0")
            .When(x => x.InsightNumber.HasValue);

        RuleFor(x => x.CreatedAfter)
            .LessThan(x => x.CreatedBefore).WithMessage("CreatedAfter must be before CreatedBefore")
            .When(x => x.CreatedAfter.HasValue && x.CreatedBefore.HasValue);

        RuleFor(x => x.UpdatedAfter)
            .LessThan(x => x.UpdatedBefore).WithMessage("UpdatedAfter must be before UpdatedBefore")
            .When(x => x.UpdatedAfter.HasValue && x.UpdatedBefore.HasValue);

        RuleFor(x => x.SortBy)
            .Must(sortBy => AllowedSortFields.Contains(sortBy))
            .WithMessage($"Sort field must be one of: {string.Join(", ", AllowedSortFields)}")
            .When(x => !string.IsNullOrEmpty(x.SortBy));
    }
}
