using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.PromptTriggers;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Application.Validators.PromptTriggers;

public class UpdatePromptTriggerDtoValidator : AbstractValidator<UpdatePromptTriggerDto>
{
    public UpdatePromptTriggerDtoValidator()
    {
        RuleFor(x => x)
            .Must(HaveAtLeastOneField)
            .WithMessage(ValidationMessages.HaveAtLeastOneField);

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage(ValidationMessages.NameRequired)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength)
            .When(x => x.Name != null);

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength).WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => x.Description != null);

        RuleFor(x => x.Priority)
            .InclusiveBetween(ValidationConstants.PriorityMinValue, ValidationConstants.PriorityMaxValue)
            .WithMessage(ValidationMessages.PriorityRange)
            .When(x => x.Priority.HasValue);

        RuleFor(x => x.InsightNumber)
            .InclusiveBetween(ValidationConstants.InsightNumberMinValue, ValidationConstants.InsightNumberMaxValue)
            .WithMessage(ValidationMessages.InsightNumberRange)
            .When(x => x.InsightNumber.HasValue);

        RuleFor(x => x.CooldownMinutes)
            .InclusiveBetween(ValidationConstants.CooldownMinutesMinValue, ValidationConstants.CooldownMinutesMaxValue)
            .WithMessage(ValidationMessages.CooldownMinutesRange)
            .When(x => x.CooldownMinutes.HasValue);

        RuleFor(x => x.DailyLimit)
            .InclusiveBetween(ValidationConstants.DailyLimitMinValue, ValidationConstants.DailyLimitMaxValue)
            .WithMessage(ValidationMessages.DailyLimitRange)
            .When(x => x.DailyLimit.HasValue);

        RuleFor(x => x.TriggerCondition)
            .IsInEnum().WithMessage(ValidationMessages.TriggerConditionRequired)
            .When(x => x.TriggerCondition.HasValue);

        RuleFor(x => x.ConditionType)
            .IsInEnum().WithMessage(ValidationMessages.ConditionTypeRequired)
            .When(x => x.ConditionType.HasValue);

        When(x => x.ConditionType == ConditionType.ScoreValue, () =>
        {
            RuleFor(x => x.ScoreType)
                .NotNull().WithMessage(ValidationMessages.ScoreTypeRequired);

            RuleFor(x => x.ScoreOperator)
                .NotNull().WithMessage(ValidationMessages.OperatorRequired);

            RuleFor(x => x.ScoreValue)
                .NotEmpty().WithMessage(ValidationMessages.ValueRequired)
                .When(x => x.ScoreOperator != OperatorType.ChangedDuringDay);

            RuleFor(x => x.ScoreValue)
                .Empty().WithMessage(ValidationMessages.ValueNotAllowed)
                .When(x => x.ScoreOperator == OperatorType.ChangedDuringDay);

            RuleFor(x => x.ScoreValue)
                .MaximumLength(ValidationConstants.ValueMaxLength).WithMessage(ValidationMessages.ValueMaxLength)
                .When(x => !string.IsNullOrEmpty(x.ScoreValue));
        });

        When(x => x.ConditionType == ConditionType.TimeOfDay, () =>
        {
            RuleFor(x => x.StartTime)
                .NotNull().WithMessage(ValidationMessages.StartTimeRequired);

            RuleFor(x => x.EndTime)
                .NotNull().WithMessage(ValidationMessages.EndTimeRequired);

            RuleFor(x => x)
                .Must(x => x.StartTime < x.EndTime)
                .WithMessage(ValidationMessages.StartTimeBeforeEndTime)
                .When(x => x.StartTime.HasValue && x.EndTime.HasValue);
        });

        When(x => x.ConditionType == ConditionType.MetricValue, () =>
        {
            RuleFor(x => x.MetricType)
                .NotNull().WithMessage(ValidationMessages.MetricTypeRequired);

            RuleFor(x => x.MetricOperator)
                .NotNull().WithMessage(ValidationMessages.OperatorRequired);

            RuleFor(x => x.MetricValue)
                .NotEmpty().WithMessage(ValidationMessages.ValueRequired)
                .When(x => x.MetricOperator != OperatorType.ChangedDuringDay);

            RuleFor(x => x.MetricValue)
                .Empty().WithMessage(ValidationMessages.ValueNotAllowed)
                .When(x => x.MetricOperator == OperatorType.ChangedDuringDay);

            RuleFor(x => x.MetricValue)
                .MaximumLength(ValidationConstants.ValueMaxLength).WithMessage(ValidationMessages.ValueMaxLength)
                .When(x => !string.IsNullOrEmpty(x.MetricValue));
        });
    }

    private static bool HaveAtLeastOneField(UpdatePromptTriggerDto dto)
    {
        return !string.IsNullOrWhiteSpace(dto.Name) ||
               dto.Description != null ||
               dto.Priority.HasValue ||
               dto.InsightNumber.HasValue ||
               dto.CooldownMinutes.HasValue ||
               dto.DailyLimit.HasValue ||
               dto.TriggerCondition.HasValue ||
               dto.ConditionType.HasValue ||
               dto.ScoreType.HasValue ||
               dto.ScoreOperator.HasValue ||
               dto.ScoreValue != null ||
               dto.StartTime.HasValue ||
               dto.EndTime.HasValue ||
               dto.MetricType.HasValue ||
               dto.MetricOperator.HasValue ||
               dto.MetricValue != null ||
               dto.AssociatedTopicIds != null;
    }
}
