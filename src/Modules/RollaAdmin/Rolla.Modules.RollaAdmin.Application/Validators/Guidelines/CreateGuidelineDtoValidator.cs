using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.Guidelines;
using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Guidelines;

public class CreateGuidelineDtoValidator : AbstractValidator<CreateGuidelineDto>
{
    public CreateGuidelineDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage(ValidationMessages.NameRequired)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength);

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength).WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Description));

        RuleFor(x => x.PromptGuidelineContent)
            .NotEmpty().WithMessage(ValidationMessages.GuidelineContentRequired)
            .MaximumLength(ValidationConstants.PromptGuidelineContentMaxLength).WithMessage(ValidationMessages.GuidelineContentMaxLength);
    }
}
