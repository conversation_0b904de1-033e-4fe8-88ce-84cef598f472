using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.Guidelines;
using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Guidelines;


public class UpdateGuidelineDtoValidator : AbstractValidator<UpdateGuidelineDto>
{
    public UpdateGuidelineDtoValidator()
    {
        RuleFor(x => x)
            .Must(HaveAtLeastOneField)
            .WithMessage(ValidationMessages.HaveAtLeastOneField);

        RuleFor(x => x.Name)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Name));

        RuleFor(x => x.PromptGuidelineContent)
            .MaximumLength(ValidationConstants.PromptGuidelineContentMaxLength)
            .WithMessage(ValidationMessages.GuidelineContentMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.PromptGuidelineContent));

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength)
            .WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Description));
    }

    private static bool HaveAtLeastOneField(UpdateGuidelineDto dto)
    {
        return !string.IsNullOrWhiteSpace(dto.Name) ||
               !string.IsNullOrWhiteSpace(dto.PromptGuidelineContent) ||
               dto.Description != null;
    }
}
