using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.Prompts;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Prompts;

public class CreatePromptDtoValidator : AbstractValidator<CreatePromptDto>
{
    public CreatePromptDtoValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage(ValidationMessages.NameRequired)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength);

        RuleFor(x => x.SystemPromptContent)
            .NotEmpty().WithMessage(ValidationMessages.SystemPromptRequired)
            .MaximumLength(ValidationConstants.SystemPromptMaxLength).WithMessage(ValidationMessages.SystemPromptMaxLength);

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength).WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Description));
    }
}
