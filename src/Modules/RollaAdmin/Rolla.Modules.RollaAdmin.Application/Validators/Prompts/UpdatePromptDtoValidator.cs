using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.Constants.Prompts;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Prompts;

public class UpdatePromptDtoValidator : AbstractValidator<UpdatePromptDto>
{
    public UpdatePromptDtoValidator()
    {
        RuleFor(x => x)
            .Must(HaveAtLeastOneField)
            .WithMessage(ValidationMessages.HaveAtLeastOneField);

        RuleFor(x => x.Name)
            .MaximumLength(ValidationConstants.NameMaxLength).WithMessage(ValidationMessages.NameMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Name));

        RuleFor(x => x.SystemPromptContent)
            .MaximumLength(ValidationConstants.SystemPromptMaxLength).WithMessage(ValidationMessages.SystemPromptMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.SystemPromptContent));

        RuleFor(x => x.Description)
            .MaximumLength(ValidationConstants.DescriptionMaxLength).WithMessage(ValidationMessages.DescriptionMaxLength)
            .When(x => !string.IsNullOrWhiteSpace(x.Description));
    }

    private static bool HaveAtLeastOneField(UpdatePromptDto dto)
    {
        return !string.IsNullOrWhiteSpace(dto.Name) ||
               !string.IsNullOrWhiteSpace(dto.SystemPromptContent) ||
               dto.Description != null;
    }
}
