using FluentValidation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;

namespace Rolla.Modules.RollaAdmin.Application.Validators.Prompts;

public class PromptFilterRequestValidator : AbstractValidator<PromptFilterRequest>
{
    private static readonly string[] ValidSortFields = 
    {
        "Name", "Description", "IsActive", "CreatedAt", "UpdatedAt"
    };

    public PromptFilterRequestValidator()
    {
        RuleFor(x => x.PageNumber)
            .GreaterThan(0)
            .WithMessage("Page number must be greater than 0");

        RuleFor(x => x.PageSize)
            .InclusiveBetween(1, 100)
            .WithMessage("Page size must be between 1 and 100");

        RuleFor(x => x.SortBy)
            .Must(BeValidSortField)
            .WithMessage($"Sort field must be one of: {string.Join(", ", ValidSortFields)}")
            .When(x => !string.IsNullOrWhiteSpace(x.SortBy));

        RuleFor(x => x.CreatedAfter)
            .LessThan(x => x.CreatedBefore)
            .WithMessage("CreatedAfter must be before CreatedBefore")
            .When(x => x.CreatedAfter.HasValue && x.CreatedBefore.HasValue);

        RuleFor(x => x.UpdatedAfter)
            .LessThan(x => x.UpdatedBefore)
            .WithMessage("UpdatedAfter must be before UpdatedBefore")
            .When(x => x.UpdatedAfter.HasValue && x.UpdatedBefore.HasValue);

        RuleFor(x => x.CreatedAfter)
            .LessThanOrEqualTo(DateTime.UtcNow)
            .WithMessage("CreatedAfter cannot be in the future")
            .When(x => x.CreatedAfter.HasValue);

        RuleFor(x => x.SearchTerm)
            .MaximumLength(100)
            .WithMessage("Search term cannot exceed 100 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.SearchTerm));
    }

    private static bool BeValidSortField(string? sortBy)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
            return true;

        return ValidSortFields.Contains(sortBy, StringComparer.OrdinalIgnoreCase);
    }
}
