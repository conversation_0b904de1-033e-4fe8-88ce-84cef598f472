using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;
using Rolla.Modules.RollaAdmin.Core.Entities.Prompts;

namespace Rolla.Modules.RollaAdmin.Application.Extensions;

public static class PromptExtensions
{
    public static PromptDto ToDto(this Prompt prompt)
    {
        return new PromptDto
        {
            Id = prompt.Id,
            Name = prompt.Name.Value,
            Description = prompt.Description,
            SystemPromptContent = prompt.SystemPromptContent.Value,
            IsActive = prompt.IsActive,
            CreatedAt = prompt.CreatedAt,
            UpdatedAt = prompt.UpdatedAt
        };
    }
}
