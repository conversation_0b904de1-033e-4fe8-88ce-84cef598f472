using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;
using Rolla.Modules.RollaAdmin.Core.Entities.Topics;

namespace Rolla.Modules.RollaAdmin.Application.Extensions;

public static class TopicExtensions
{
    public static TopicDto ToDto(this Topic topic)
    {
        return new TopicDto
        {
            Id = topic.Id,
            Name = topic.Name.Value,
            Description = topic.Description,
            Prompt = topic.Prompt.Value,
            Priority = topic.Priority.Value,
            IsActive = topic.IsActive,
            CreatedAt = topic.CreatedAt,
            UpdatedAt = topic.UpdatedAt
        };
    }
}
