<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Rolla.Modules.RollaAdmin.Core\Rolla.Modules.RollaAdmin.Core.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\Rolla.BuildingBlocks.Application\Rolla.BuildingBlocks.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>Rolla.Modules.RollaAdmin.Application</RootNamespace>
  </PropertyGroup>

</Project>
