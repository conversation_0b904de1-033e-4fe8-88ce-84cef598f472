using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.Guidelines;

public interface IGuidelineService
{
    Task<Result<PagedResult<GuidelineDto>>> GetAllGuidelinesAsync(GuidelineFilterRequest request, CancellationToken cancellationToken);
    Task<Result<GuidelineDto>> GetGuidelineByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<GuidelineDto>> GetActiveGuidelineAsync(CancellationToken cancellationToken);
    Task<Result<GuidelineDto>> CreateGuidelineAsync(CreateGuidelineDto createDto, CancellationToken cancellationToken);
    Task<Result<GuidelineDto>> UpdateGuidelineAsync(Guid id, UpdateGuidelineDto updateDto, CancellationToken cancellationToken);
    Task<Result<bool>> ActivateGuidelineAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<bool>> DeleteGuidelineAsync(Guid id, CancellationToken cancellationToken);
}
