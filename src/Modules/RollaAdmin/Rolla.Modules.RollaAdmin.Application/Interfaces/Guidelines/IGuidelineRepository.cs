using Rolla.BuildingBlocks.Application.Common;
using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;
using Rolla.Modules.RollaAdmin.Core.Entities.Guidelines;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.Guidelines;

public interface IGuidelineRepository
{
    Task<PagedResult<Guideline>> GetAllAsync(GuidelineFilterRequest request, CancellationToken cancellationToken);
    Task<Guideline?> GetByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<Guideline?> GetActiveGuidelineAsync(CancellationToken cancellationToken);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken);

    Task AddAsync(Guideline guideline, CancellationToken cancellationToken);

    Task UpdateAsync(Guideline guideline, CancellationToken cancellationToken);
    Task DeleteAsync(Guideline guideline, CancellationToken cancellationToken);

}
