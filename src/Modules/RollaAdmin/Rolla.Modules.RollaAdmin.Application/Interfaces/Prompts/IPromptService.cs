using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.Prompts;

public interface IPromptService
{
    Task<Result<PagedResult<PromptDto>>> GetAllPromptsAsync(PromptFilterRequest request, CancellationToken cancellationToken);
    Task<Result<PromptDto>> GetPromptByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<PromptDto>> GetActivePromptAsync(CancellationToken cancellationToken);
    Task<Result<PromptDto>> CreatePromptAsync(CreatePromptDto dto, CancellationToken cancellationToken);
    Task<Result<PromptDto>> UpdatePromptAsync(Guid id, UpdatePromptDto dto, CancellationToken cancellationToken);
    Task<Result<bool>> ActivatePromptAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<bool>> DeactivatePromptAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<bool>> DeletePromptAsync(Guid id, CancellationToken cancellationToken);
}
