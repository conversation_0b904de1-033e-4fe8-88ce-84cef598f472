using Rolla.BuildingBlocks.Application.Common;
using Rolla.Modules.RollaAdmin.Application.DTOs.Topics;
using Rolla.Modules.RollaAdmin.Core.Entities.Topics;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.Topics;

public interface ITopicRepository
{
    Task<PagedResult<Topic>> GetAllAsync(TopicFilterRequest request, CancellationToken cancellationToken);
    Task<Topic?> GetByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<List<Topic>> GetActiveTopicsAsync(CancellationToken cancellationToken);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken);

    Task AddAsync(Topic topic, CancellationToken cancellationToken);
    Task UpdateAsync(Topic topic, CancellationToken cancellationToken);
    Task DeleteAsync(Topic topic, CancellationToken cancellationToken);
}
