using Rolla.BuildingBlocks.Application.Common;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.PromptTriggers;

public interface IPromptTriggerRepository
{
    Task<PagedResult<PromptTrigger>> GetAllAsync(PromptTriggerFilterRequest request, CancellationToken cancellationToken);
    Task<PromptTrigger?> GetByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken);

    Task AddAsync(PromptTrigger promptTrigger, CancellationToken cancellationToken);
    Task UpdateAsync(PromptTrigger promptTrigger, CancellationToken cancellationToken);
    Task DeleteAsync(PromptTrigger promptTrigger, CancellationToken cancellationToken);
}
