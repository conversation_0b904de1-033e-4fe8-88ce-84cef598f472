using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;

namespace Rolla.Modules.RollaAdmin.Application.Interfaces.PromptTriggers;

public interface IPromptTriggerService
{
    Task<Result<PagedResult<PromptTriggerDto>>> GetAllPromptTriggersAsync(PromptTriggerFilterRequest request, CancellationToken cancellationToken);
    Task<Result<PromptTriggerDto>> GetPromptTriggerByIdAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<PromptTriggerDto>> CreatePromptTriggerAsync(CreatePromptTriggerDto dto, CancellationToken cancellationToken);
    Task<Result<PromptTriggerDto>> UpdatePromptTriggerAsync(Guid id, UpdatePromptTriggerDto dto, CancellationToken cancellationToken);
    Task<Result<bool>> ActivatePromptTriggerAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<bool>> DeactivatePromptTriggerAsync(Guid id, CancellationToken cancellationToken);
    Task<Result<bool>> DeletePromptTriggerAsync(Guid id, CancellationToken cancellationToken);
}
