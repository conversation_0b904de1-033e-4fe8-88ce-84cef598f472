using Rolla.BuildingBlocks.Application.Common;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;

public class PromptTriggerFilterRequest : PaginationRequest
{
    public string? SearchTerm { get; set; }
    public bool? IsActive { get; set; }
    public int? MinPriority { get; set; }
    public int? MaxPriority { get; set; }
    public int? InsightNumber { get; set; }
    public TriggerCondition? TriggerCondition { get; set; }
    public ConditionType? ConditionType { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? UpdatedAfter { get; set; }
    public DateTime? UpdatedBefore { get; set; }
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
    
    public bool HasSearchTerm => !string.IsNullOrWhiteSpace(SearchTerm);
    public bool HasActiveFilter => IsActive.HasValue;
    public bool HasPriorityFilter => MinPriority.HasValue || MaxPriority.HasValue;
    public bool HasInsightNumberFilter => InsightNumber.HasValue;
    public bool HasTriggerConditionFilter => TriggerCondition.HasValue;
    public bool HasConditionTypeFilter => ConditionType.HasValue;
}
