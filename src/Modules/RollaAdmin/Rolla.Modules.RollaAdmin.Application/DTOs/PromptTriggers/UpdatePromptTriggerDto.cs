using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;

public record UpdatePromptTriggerDto
{
    public string? Name { get; init; }
    public string? Description { get; init; }
    public int? Priority { get; init; }
    public int? InsightNumber { get; init; }
    public int? CooldownMinutes { get; init; }
    public int? DailyLimit { get; init; }
    public TriggerCondition? TriggerCondition { get; init; }
    public ConditionType? ConditionType { get; init; }

    // Score Value specific properties
    public ScoreType? ScoreType { get; init; }
    public OperatorType? ScoreOperator { get; init; }
    public string? ScoreValue { get; init; }

    // Time of Day specific properties
    public TimeOnly? StartTime { get; init; }
    public TimeOnly? EndTime { get; init; }

    // Metric Value specific properties
    public MetricType? MetricType { get; init; }
    public OperatorType? MetricOperator { get; init; }
    public string? MetricValue { get; init; }

    // Associated Topics
    public List<Guid>? AssociatedTopicIds { get; init; }
}
