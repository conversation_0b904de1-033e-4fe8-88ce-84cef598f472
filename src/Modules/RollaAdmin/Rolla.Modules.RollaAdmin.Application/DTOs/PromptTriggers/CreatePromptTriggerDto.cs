using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;

namespace Rolla.Modules.RollaAdmin.Application.DTOs.PromptTriggers;

public record CreatePromptTriggerDto
{
    public required string Name { get; init; }
    public string? Description { get; init; }
    public int Priority { get; init; } = 1;
    public int InsightNumber { get; init; }
    public int CooldownMinutes { get; init; }
    public int DailyLimit { get; init; }
    public TriggerCondition TriggerCondition { get; init; }
    public ConditionType ConditionType { get; init; }

    public ScoreType? ScoreType { get; init; }
    public OperatorType? ScoreOperator { get; init; }
    public string? ScoreValue { get; init; }

    public TimeOnly? StartTime { get; init; }
    public TimeOnly? EndTime { get; init; }

    public MetricType? MetricType { get; init; }
    public OperatorType? MetricOperator { get; init; }
    public string? MetricValue { get; init; }

    public List<Guid> AssociatedTopicIds { get; init; } = new();
}
