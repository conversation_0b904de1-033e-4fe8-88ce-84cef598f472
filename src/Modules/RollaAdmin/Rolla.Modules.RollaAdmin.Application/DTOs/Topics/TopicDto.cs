namespace Rolla.Modules.RollaAdmin.Application.DTOs.Topics;

public record TopicDto
{
    public Guid Id { get; init; }
    public required string Name { get; init; }
    public string? Description { get; init; }
    public required string Prompt { get; init; }
    public int Priority { get; init; }
    public bool IsActive { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
}
