namespace Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;


public record GuidelineDto
{
    public Guid Id { get; init; }
    public required string Name { get; init; }
    public string? Description { get; init; }
    public required string PromptGuidelineContent { get; init; }
    public bool IsActive { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? UpdatedAt { get; init; }
}
