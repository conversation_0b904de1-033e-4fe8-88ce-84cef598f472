using Rolla.BuildingBlocks.Application.Common;

namespace Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;

public class GuidelineFilterRequest : PaginationRequest
{
    public string? SearchTerm { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? UpdatedAfter { get; set; }
    public DateTime? UpdatedBefore { get; set; }
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
    public bool HasSearchTerm => !string.IsNullOrWhiteSpace(SearchTerm);
    public bool HasActiveFilter => IsActive.HasValue;
}
