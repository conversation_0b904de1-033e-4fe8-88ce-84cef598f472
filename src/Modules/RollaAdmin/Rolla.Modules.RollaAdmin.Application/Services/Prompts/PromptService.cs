using FluentValidation;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Validation;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;
using Rolla.Modules.RollaAdmin.Application.Extensions;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Prompts;
using Rolla.Modules.RollaAdmin.Core.Entities.Prompts;

namespace Rolla.Modules.RollaAdmin.Application.Services.Prompts;

public class PromptService(
    IPromptRepository promptRepository,
    IValidator<CreatePromptDto> createValidator,
    IValidator<UpdatePromptDto> updateValidator,
    IValidator<PromptFilterRequest> filterValidator,
    ILogger<PromptService> logger) : IPromptService
{
    private readonly IPromptRepository _promptRepository = promptRepository;
    private readonly IValidator<CreatePromptDto> _createValidator = createValidator;
    private readonly IValidator<UpdatePromptDto> _updateValidator = updateValidator;
    private readonly IValidator<PromptFilterRequest> _filterValidator = filterValidator;
    private readonly ILogger<PromptService> _logger = logger;

    public async Task<Result<PagedResult<PromptDto>>> GetAllPromptsAsync(PromptFilterRequest request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _filterValidator.ValidateAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid filter request: {Errors}", string.Join(", ", errors));
                return Result<PagedResult<PromptDto>>.Failure(errors);
            }


            var pagedPrompts = await _promptRepository.GetAllAsync(request, cancellationToken);
            var promptDtos = pagedPrompts.Items.Select(p => p.ToDto());

            var result = PagedResult<PromptDto>.Create(
                promptDtos,
                pagedPrompts.TotalCount,
                pagedPrompts.PageNumber,
                pagedPrompts.PageSize);


            return Result<PagedResult<PromptDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve prompts with filters: {SearchTerm}, {IsActive}",
                request.SearchTerm, request.IsActive);
            return Result<PagedResult<PromptDto>>.Failure($"Failed to retrieve prompts: {ex.Message}");
        }
    }
    public async Task<Result<PromptDto>> GetPromptByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var prompt = await _promptRepository.GetByIdAsync(id, cancellationToken);
            if (prompt == null)
            {
                _logger.LogWarning("Error occurred while retrieving prompt with ID: {PromptId}", id);
                return Result<PromptDto>.Failure("Prompt not found");
            }

            return Result<PromptDto>.Success(prompt.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving prompt with ID: {PromptId}", id);
            return Result<PromptDto>.Failure($"Failed to retrieve prompt: {ex.Message}");
        }
    }
    public async Task<Result<PromptDto>> GetActivePromptAsync(CancellationToken cancellationToken)
    {
        try
        {
            var prompt = await _promptRepository.GetActivePromptAsync(cancellationToken);
            if (prompt == null)
            {
                _logger.LogWarning("Error occurred while retrieving active prompt");
                return Result<PromptDto>.Failure("No active prompt found");
            }

            return Result<PromptDto>.Success(prompt.ToDto());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while retrieving active prompt");
            return Result<PromptDto>.Failure($"Failed to retrieve active prompt: {ex.Message}");
        }
    }
    public async Task<Result<PromptDto>> CreatePromptAsync(CreatePromptDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _createValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid create request for prompt {PromptName}: {Errors}",
                    dto.Name, string.Join(", ", errors));
                return Result<PromptDto>.Failure(errors);
            }

            var nameExists = await _promptRepository.ExistsByNameAsync(dto.Name, cancellationToken);
            if (nameExists)
            {
                _logger.LogWarning("Prompt with name {PromptName} already exists", dto.Name);
                return Result<PromptDto>.Failure("A prompt with this name already exists");
            }

            try
            {
                var prompt = new Prompt(dto.Name, dto.Description, dto.SystemPromptContent);
                await _promptRepository.AddAsync(prompt, cancellationToken);

                return Result<PromptDto>.Success(prompt.ToDto());
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid prompt data: {Message}", ex.Message);
                return Result<PromptDto>.Failure(ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating prompt: {PromptName}", dto.Name);
            return Result<PromptDto>.Failure($"Failed to create prompt: {ex.Message}");
        }
    }
    public async Task<Result<PromptDto>> UpdatePromptAsync(Guid id, UpdatePromptDto dto, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await _updateValidator.ValidateAsync(dto, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Invalid update request for prompt {PromptId}: {Errors}",
                    id, string.Join(", ", errors));
                return Result<PromptDto>.Failure(errors);
            }

            var prompt = await _promptRepository.GetByIdAsync(id, cancellationToken);
            if (prompt == null)
            {
                _logger.LogWarning("Prompt not found for update with ID: {PromptId}", id);
                return Result<PromptDto>.Failure("Prompt not found");
            }

            if (!string.IsNullOrWhiteSpace(dto.Name))
            {
                var nameExists = await _promptRepository.ExistsByNameAsync(dto.Name, id, cancellationToken);
                if (nameExists)
                {
                    _logger.LogWarning("Prompt with name {PromptName} already exists", dto.Name);
                    return Result<PromptDto>.Failure("A prompt with this name already exists");
                }
            }

            try
            {
                prompt.Update(dto.Name, dto.Description, dto.SystemPromptContent);
                await _promptRepository.UpdateAsync(prompt, cancellationToken);

                return Result<PromptDto>.Success(prompt.ToDto());
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid prompt update data: {Message}", ex.Message);
                return Result<PromptDto>.Failure(ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating prompt with ID: {PromptId}", id);
            return Result<PromptDto>.Failure($"Failed to update prompt: {ex.Message}");
        }
    }

    public async Task<Result<bool>> ActivatePromptAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var prompt = await _promptRepository.GetByIdAsync(id, cancellationToken);
            if (prompt == null)
            {
                _logger.LogWarning("Prompt not found for activation with ID: {PromptId}", id);
                return Result<bool>.Failure("Prompt not found");
            }

            var existingPrompt = await _promptRepository.GetActivePromptAsync(cancellationToken);

            if (existingPrompt != null && existingPrompt.Id != id)
            {
                existingPrompt.Deactivate();
                await _promptRepository.UpdateAsync(existingPrompt, cancellationToken);
            }

            prompt.Activate();
            await _promptRepository.UpdateAsync(prompt, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while activating prompt with ID: {PromptId}", id);
            return Result<bool>.Failure($"Failed to activate prompt: {ex.Message}");
        }
    }

    public async Task<Result<bool>> DeactivatePromptAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var prompt = await _promptRepository.GetByIdAsync(id, cancellationToken);
            if (prompt == null)
            {
                _logger.LogWarning("Prompt not found for deactivation with ID: {PromptId}", id);
                return Result<bool>.Failure("Prompt not found");
            }

            prompt.Deactivate();
            await _promptRepository.UpdateAsync(prompt, cancellationToken);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deactivating prompt with ID: {PromptId}", id);
            return Result<bool>.Failure($"Failed to deactivate prompt: {ex.Message}");
        }
    }

    public async Task<Result<bool>> DeletePromptAsync(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var prompt = await _promptRepository.GetByIdAsync(id, cancellationToken);
            if (prompt == null)
            {
                _logger.LogWarning("Prompt not found for deletion with ID: {PromptId}", id);
                return Result<bool>.Failure("Prompt not found");
            }

            await _promptRepository.DeleteAsync(prompt, cancellationToken);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting prompt with ID: {PromptId}", id);
            return Result<bool>.Failure($"Failed to delete prompt: {ex.Message}");
        }
    }
}
