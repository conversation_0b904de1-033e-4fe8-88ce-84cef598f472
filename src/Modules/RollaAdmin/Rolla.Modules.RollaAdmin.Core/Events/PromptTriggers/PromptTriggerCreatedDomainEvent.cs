using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.Events.PromptTriggers;

public sealed record PromptTriggerCreatedDomainEvent(
    Guid PromptTriggerId,
    string Name,
    string? Description,
    int Priority,
    int InsightNumber,
    TriggerCondition TriggerCondition,
    ConditionType ConditionType) : IDomainEvent
{
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
    public Guid EventId { get; } = Guid.NewGuid();
}
