using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.ValueObjects;

public sealed class Content : ValueObject
{
    public string Value { get; }

    private Content(string value)
    {
        Value = value;
    }

    public static Content Create(string value)
    {
        return Create(value, 5000);
    }

    public static Content Create(string value, int maxLength)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Content cannot be null or empty", nameof(value));

        if (value.Length > maxLength)
            throw new ArgumentException($"Content cannot exceed {maxLength} characters", nameof(value));

        return new Content(value.Trim());
    }

    public static implicit operator string(Content content) => content.Value;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }

    public override string ToString() => Value;
}
