using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.ValueObjects;

public sealed class Priority : ValueObject
{
    public int Value { get; }

    private Priority(int value)
    {
        Value = value;
    }

    public static Priority Create(int value)
    {
        if (value < 0)
            throw new ArgumentException("Priority must be non-negative", nameof(value));

        return new Priority(value);
    }

    public static implicit operator int(Priority priority) => priority.Value;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }

    public override string ToString() => Value.ToString();
}
