using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.ValueObjects;

public sealed class Name : ValueObject
{
    public string Value { get; }

    private Name(string value)
    {
        Value = value;
    }

    public static Name Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Name cannot be null or empty", nameof(value));

        if (value.Length > 100)
            throw new ArgumentException("Name cannot exceed 100 characters", nameof(value));

        return new Name(value.Trim());
    }

    public static implicit operator string(Name name) => name.Value;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }

    public override string ToString() => Value;
}
