using Rolla.Modules.RollaAdmin.Core.Events.Prompts;
using Rolla.Modules.RollaAdmin.Core.ValueObjects;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.Entities.Prompts;

public class Prompt : AggregateRoot<Guid>
{
    public Name Name { get; private set; }
    public string? Description { get; private set; }
    public Content SystemPromptContent { get; private set; }
    public bool IsActive { get; private set; }

    protected Prompt()
    {
        Name = null!;
        SystemPromptContent = null!;
    }

    public Prompt(string name, string? description, string systemPromptContent)
    {
        Id = Guid.NewGuid();
        Name = Name.Create(name);
        Description = description;
        SystemPromptContent = Content.Create(systemPromptContent);
        IsActive = false;

        AddDomainEvent(new PromptCreatedDomainEvent(Id, Name, Description, SystemPromptContent));
    }

    public void Activate()
    {
        if (IsActive)
            return;

        IsActive = true;
        AddDomainEvent(new PromptActivatedDomainEvent(Id, Name));
    }

    public void Deactivate()
    {
        if (!IsActive)
            return;

        IsActive = false;
        AddDomainEvent(new PromptDeactivatedDomainEvent(Id, Name));
    }

    public void Update(string? name, string? description, string? systemPromptContent)
    {
        var hasChanges = false;

        if (!string.IsNullOrWhiteSpace(name) && name != Name.Value)
        {
            Name = Name.Create(name);
            hasChanges = true;
        }

        if (description != Description)
        {
            Description = description;
            hasChanges = true;
        }

        if (!string.IsNullOrWhiteSpace(systemPromptContent) && systemPromptContent != SystemPromptContent.Value)
        {
            SystemPromptContent = Content.Create(systemPromptContent);
            hasChanges = true;
        }

        if (hasChanges)
        {
            AddDomainEvent(new PromptUpdatedDomainEvent(Id, Name, Description, SystemPromptContent));
        }
    }
}
