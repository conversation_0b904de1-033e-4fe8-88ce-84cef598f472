using Rolla.Modules.RollaAdmin.Core.Events.Topics;
using Rolla.Modules.RollaAdmin.Core.ValueObjects;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.Entities.Topics;

public class Topic : AggregateRoot<Guid>
{
    public Name Name { get; private set; }
    public string? Description { get; private set; }
    public Content Prompt { get; private set; }
    public Priority Priority { get; private set; }
    public bool IsActive { get; private set; }

    protected Topic()
    {
        Name = null!;
        Prompt = null!;
        Priority = null!;
    }

    public Topic(string name, string? description, string prompt, int priority)
    {
        Id = Guid.NewGuid();
        Name = Name.Create(name);
        Description = description;
        Prompt = Content.Create(prompt);
        Priority = Priority.Create(priority);
        IsActive = false;

        AddDomainEvent(new TopicCreatedDomainEvent(Id, Name, Description, Prompt, Priority));
    }

    public void Activate()
    {
        if (IsActive)
            return;

        IsActive = true;
        AddDomainEvent(new TopicActivatedDomainEvent(Id, Name, Priority));
    }

    public void Deactivate()
    {
        if (!IsActive)
            return;

        IsActive = false;
        AddDomainEvent(new TopicDeactivatedDomainEvent(Id, Name));
    }

    public void Update(string? name, string? description, string? prompt, int? priority)
    {
        var hasChanges = false;

        if (!string.IsNullOrWhiteSpace(name) && name != Name.Value)
        {
            Name = Name.Create(name);
            hasChanges = true;
        }

        if (description != Description)
        {
            Description = description;
            hasChanges = true;
        }

        if (!string.IsNullOrWhiteSpace(prompt) && prompt != Prompt.Value)
        {
            Prompt = Content.Create(prompt);
            hasChanges = true;
        }

        if (priority.HasValue && priority.Value != Priority.Value)
        {
            Priority = Priority.Create(priority.Value);
            hasChanges = true;
        }

        if (hasChanges)
        {
            AddDomainEvent(new TopicUpdatedDomainEvent(Id, Name, Description, Prompt, Priority));
        }
    }
}
