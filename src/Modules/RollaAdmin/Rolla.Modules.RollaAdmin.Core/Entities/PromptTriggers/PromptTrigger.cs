using System.Text.Json;
using Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers.Enums;
using Rolla.Modules.RollaAdmin.Core.Events.PromptTriggers;
using Rolla.Modules.RollaAdmin.Core.ValueObjects;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.Entities.PromptTriggers;

public class PromptTrigger : AggregateRoot<Guid>
{
    public Name Name { get; private set; }
    public string? Description { get; private set; }
    public Priority Priority { get; private set; }
    public int InsightNumber { get; private set; }
    public int CooldownMinutes { get; private set; }
    public int DailyLimit { get; private set; }
    public bool IsActive { get; private set; }
    public TriggerCondition TriggerCondition { get; private set; }
    public ConditionType ConditionType { get; private set; }
    public ScoreType? ScoreType { get; private set; }
    public OperatorType? ScoreOperator { get; private set; }
    public string? ScoreValue { get; private set; }
    public TimeOnly? StartTime { get; private set; }
    public TimeOnly? EndTime { get; private set; }
    public MetricType? MetricType { get; private set; }
    public OperatorType? MetricOperator { get; private set; }
    public string? MetricValue { get; private set; }
    private string? _associatedTopicsJson;

    public List<Guid> AssociatedTopicIds
    {
        get => string.IsNullOrEmpty(_associatedTopicsJson)
            ? new List<Guid>()
            : JsonSerializer.Deserialize<List<Guid>>(_associatedTopicsJson) ?? new List<Guid>();
        private set => _associatedTopicsJson = value.Count == 0
            ? "[]"
            : JsonSerializer.Serialize(value);
    }

    protected PromptTrigger()
    {
        Name = null!;
        Priority = null!;
        AssociatedTopicIds = new List<Guid>();
    }

    public PromptTrigger(
        string name,
        string? description,
        int priority,
        int insightNumber,
        int cooldownMinutes,
        int dailyLimit,
        TriggerCondition triggerCondition,
        ConditionType conditionType,
        List<Guid>? associatedTopicIds = null)
    {
        ValidateBasicProperties(name, priority, insightNumber, cooldownMinutes, dailyLimit);

        Id = Guid.NewGuid();
        Name = Name.Create(name);
        Description = description;
        Priority = Priority.Create(priority);
        InsightNumber = insightNumber;
        CooldownMinutes = cooldownMinutes;
        DailyLimit = dailyLimit;
        IsActive = false;
        TriggerCondition = triggerCondition;
        ConditionType = conditionType;
        AssociatedTopicIds = associatedTopicIds ?? new List<Guid>();

        AddDomainEvent(new PromptTriggerCreatedDomainEvent(Id, Name, Description, Priority, InsightNumber, TriggerCondition, ConditionType));
    }

    public void SetScoreValueCondition(ScoreType scoreType, OperatorType operatorType, string? value)
    {
        if (ConditionType != ConditionType.ScoreValue)
            throw new InvalidOperationException("Can only set score value condition when ConditionType is ScoreValue");

        if (operatorType == OperatorType.ChangedDuringDay && !string.IsNullOrEmpty(value))
            throw new ArgumentException("Value must be null when operator is ChangedDuringDay");

        if (operatorType != OperatorType.ChangedDuringDay && string.IsNullOrEmpty(value))
            throw new ArgumentException("Value is required when operator is not ChangedDuringDay");

        ScoreType = scoreType;
        ScoreOperator = operatorType;
        ScoreValue = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetTimeOfDayCondition(TimeOnly startTime, TimeOnly endTime)
    {
        if (ConditionType != ConditionType.TimeOfDay)
            throw new InvalidOperationException("Can only set time of day condition when ConditionType is TimeOfDay");

        if (startTime >= endTime)
            throw new ArgumentException("Start time must be before end time");

        StartTime = startTime;
        EndTime = endTime;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetMetricValueCondition(MetricType metricType, OperatorType operatorType, string? value)
    {
        if (ConditionType != ConditionType.MetricValue)
            throw new InvalidOperationException("Can only set metric value condition when ConditionType is MetricValue");

        if (operatorType == OperatorType.ChangedDuringDay && !string.IsNullOrEmpty(value))
            throw new ArgumentException("Value must be null when operator is ChangedDuringDay");

        if (operatorType != OperatorType.ChangedDuringDay && string.IsNullOrEmpty(value))
            throw new ArgumentException("Value is required when operator is not ChangedDuringDay");

        MetricType = metricType;
        MetricOperator = operatorType;
        MetricValue = value;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        if (IsActive)
            return;

        IsActive = true;
        AddDomainEvent(new PromptTriggerActivatedDomainEvent(Id, Name, Priority));
    }

    public void Deactivate()
    {
        if (!IsActive)
            return;

        IsActive = false;
        AddDomainEvent(new PromptTriggerDeactivatedDomainEvent(Id, Name));
    }

    public void Update(
        string? name,
        string? description,
        int? priority,
        int? insightNumber,
        int? cooldownMinutes,
        int? dailyLimit,
        TriggerCondition? triggerCondition,
        List<Guid>? associatedTopicIds)
    {
        var hasChanges = false;

        if (!string.IsNullOrWhiteSpace(name) && name != Name.Value)
        {
            Name = Name.Create(name);
            hasChanges = true;
        }

        if (description != Description)
        {
            Description = description;
            hasChanges = true;
        }

        if (priority.HasValue && priority.Value != Priority.Value)
        {
            if (priority.Value < 1)
                throw new ArgumentException("Priority must be greater than 0", nameof(priority));
            Priority = Priority.Create(priority.Value);
            hasChanges = true;
        }

        if (insightNumber.HasValue && insightNumber.Value != InsightNumber)
        {
            if (insightNumber.Value < 1)
                throw new ArgumentException("Insight number must be greater than 0", nameof(insightNumber));
            InsightNumber = insightNumber.Value;
            hasChanges = true;
        }

        if (cooldownMinutes.HasValue && cooldownMinutes.Value != CooldownMinutes)
        {
            if (cooldownMinutes.Value < 0)
                throw new ArgumentException("Cooldown minutes must be non-negative", nameof(cooldownMinutes));
            CooldownMinutes = cooldownMinutes.Value;
            hasChanges = true;
        }

        if (dailyLimit.HasValue && dailyLimit.Value != DailyLimit)
        {
            if (dailyLimit.Value < 1)
                throw new ArgumentException("Daily limit must be greater than 0", nameof(dailyLimit));
            DailyLimit = dailyLimit.Value;
            hasChanges = true;
        }

        if (triggerCondition.HasValue && triggerCondition.Value != TriggerCondition)
        {
            TriggerCondition = triggerCondition.Value;
            hasChanges = true;
        }

        if (associatedTopicIds != null && !associatedTopicIds.SequenceEqual(AssociatedTopicIds))
        {
            AssociatedTopicIds = associatedTopicIds;
            hasChanges = true;
        }

        _ = hasChanges;
    }

    private static void ValidateBasicProperties(string name, int priority, int insightNumber, int cooldownMinutes, int dailyLimit)
    {
        Name.Create(name);

        Priority.Create(priority);

        if (insightNumber < 1)
            throw new ArgumentException("Insight number must be greater than 0", nameof(insightNumber));

        if (cooldownMinutes < 0)
            throw new ArgumentException("Cooldown minutes must be non-negative", nameof(cooldownMinutes));

        if (dailyLimit < 1)
            throw new ArgumentException("Daily limit must be greater than 0", nameof(dailyLimit));
    }
}
