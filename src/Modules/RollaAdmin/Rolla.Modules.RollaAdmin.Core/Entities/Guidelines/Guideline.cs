using Rolla.Modules.RollaAdmin.Core.Events.Guidelines;
using Rolla.Modules.RollaAdmin.Core.ValueObjects;
using Rolla.Shared.Kernel.Domain;

namespace Rolla.Modules.RollaAdmin.Core.Entities.Guidelines;

public class Guideline : AggregateRoot<Guid>
{
    public Name Name { get; private set; }
    public string? Description { get; private set; }
    public Content PromptGuidelineContent { get; private set; }
    public bool IsActive { get; private set; } = false;

    public Guideline(Guid id, string name, string? description, string promptGuidelineContent)
    {
        Id = id;
        Name = Name.Create(name);
        Description = description;
        PromptGuidelineContent = Content.Create(promptGuidelineContent);
        IsActive = false;

        AddDomainEvent(new GuidelineCreatedDomainEvent(Id, Name, Description, PromptGuidelineContent));
    }

    private Guideline()
    {
        Name = null!;
        PromptGuidelineContent = null!;
    }

    public void Activate()
    {
        if (IsActive)
            return;

        IsActive = true;
        AddDomainEvent(new GuidelineActivatedDomainEvent(Id, Name));
    }

    public void Deactivate()
    {
        if (!IsActive)
            return;

        IsActive = false;
        AddDomainEvent(new GuidelineDeactivatedDomainEvent(Id, Name));
    }

    public void Update(string? name, string? description, string? content)
    {
        var hasChanges = false;

        if (!string.IsNullOrWhiteSpace(name) && name != Name.Value)
        {
            Name = Name.Create(name);
            hasChanges = true;
        }

        if (description != Description)
        {
            Description = description;
            hasChanges = true;
        }

        if (!string.IsNullOrWhiteSpace(content) && content != PromptGuidelineContent.Value)
        {
            PromptGuidelineContent = Content.Create(content);
            hasChanges = true;
        }

        if (hasChanges)
        {
            AddDomainEvent(new GuidelineUpdatedDomainEvent(Id, Name, Description, PromptGuidelineContent));
        }
    }
}
