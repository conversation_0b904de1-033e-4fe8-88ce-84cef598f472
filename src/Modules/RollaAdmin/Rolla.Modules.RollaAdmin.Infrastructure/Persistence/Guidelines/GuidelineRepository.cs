using Microsoft.EntityFrameworkCore;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.Modules.RollaAdmin.Application.DTOs.Guidelines;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Guidelines;
using Rolla.Modules.RollaAdmin.Core.Entities.Guidelines;
using Rolla.Modules.RollaAdmin.Infrastructure.Database;

namespace Rolla.Modules.RollaAdmin.Infrastructure.Persistence.Guidelines;

public class GuidelineRepository(RollaAdminDbContext context) : IGuidelineRepository
{

    private readonly RollaAdminDbContext _context = context;

    public async Task<Guideline?> GetByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        return await _context.Guidelines
            .FirstOrDefaultAsync(g => g.Id == id, cancellationToken);
    }

    public async Task<PagedResult<Guideline>> GetAllAsync(GuidelineFilterRequest request, CancellationToken cancellationToken)
    {

        var query = _context.Guidelines
            .AsNoTracking();

        if (request.HasSearchTerm)
        {
            var searchTerm = request.SearchTerm!;
            query = query.Where(g =>
                g.Name.Value.Contains(searchTerm) ||
                (g.Description != null && g.Description.Contains(searchTerm)) ||
                g.PromptGuidelineContent.Value.Contains(searchTerm));
        }


        if (request.HasActiveFilter)
        {
            query = query.Where(g => g.IsActive == request.IsActive);
        }

        if (request.CreatedAfter.HasValue)
        {
            query = query.Where(g => g.CreatedAt >= request.CreatedAfter.Value);
        }

        if (request.CreatedBefore.HasValue)
        {
            query = query.Where(g => g.CreatedAt <= request.CreatedBefore.Value);
        }

        if (request.UpdatedAfter.HasValue)
        {
            query = query.Where(g => g.UpdatedAt >= request.UpdatedAfter.Value);
        }

        if (request.UpdatedBefore.HasValue)
        {
            query = query.Where(g => g.UpdatedAt <= request.UpdatedBefore.Value);
        }

        query = ApplySorting(query, request.SortBy, request.SortDescending);

        var totalCount = await query.CountAsync(cancellationToken);


        var items = await query
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync(cancellationToken);

        return PagedResult<Guideline>.Create(items, totalCount, request.PageNumber, request.PageSize);
    }

    public async Task<Guideline?> GetActiveGuidelineAsync(CancellationToken cancellationToken)
    {
        return await _context.Guidelines
            .AsNoTracking()
            .FirstOrDefaultAsync(g => g.IsActive, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);

        return await _context.Guidelines
            .AsNoTracking()
            .AnyAsync(g => g.Name.Value == name, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);

        return await _context.Guidelines
            .AsNoTracking()
            .AnyAsync(g => g.Name.Value == name && g.Id != excludeId, cancellationToken);
    }
    public async Task AddAsync(Guideline guideline, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(guideline);

        _context.Guidelines.Add(guideline);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Guideline guideline, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(guideline);

        _context.Guidelines.Update(guideline);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Guideline guideline, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(guideline);

        _context.Guidelines.Remove(
            guideline);
        await _context.SaveChangesAsync(cancellationToken);
    }

    private static IQueryable<Guideline> ApplySorting(IQueryable<Guideline> query, string? sortBy, bool sortDescending)
    {

        return sortBy?.ToLower() switch
        {
            "name" => sortDescending
                ? query.OrderByDescending(g => g.Name).ThenByDescending(g => g.Id)
                : query.OrderBy(g => g.Name).ThenBy(g => g.Id),

            "description" => sortDescending
                ? query.OrderByDescending(g => g.Description).ThenByDescending(g => g.Id)
                : query.OrderBy(g => g.Description).ThenBy(g => g.Id),

            "isactive" => sortDescending
                ? query.OrderByDescending(g => g.IsActive).ThenByDescending(g => g.Id)
                : query.OrderBy(g => g.IsActive).ThenBy(g => g.Id),

            "updatedat" => sortDescending
                ? query.OrderByDescending(g => g.UpdatedAt).ThenByDescending(g => g.Id)
                : query.OrderBy(g => g.UpdatedAt).ThenBy(g => g.Id),

            "createdat" or _ => sortDescending
                ? query.OrderByDescending(g => g.CreatedAt).ThenByDescending(g => g.Id)
                : query.OrderBy(g => g.CreatedAt).ThenBy(g => g.Id)
        };
    }
}
