using Microsoft.EntityFrameworkCore;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.Modules.RollaAdmin.Application.DTOs.Prompts;
using Rolla.Modules.RollaAdmin.Application.Interfaces.Prompts;
using Rolla.Modules.RollaAdmin.Core.Entities.Prompts;
using Rolla.Modules.RollaAdmin.Infrastructure.Database;

namespace Rolla.Modules.RollaAdmin.Infrastructure.Persistence.Prompts;

public class PromptRepository(RollaAdminDbContext context) : IPromptRepository
{
    private readonly RollaAdminDbContext _context = context;

    public async Task<Prompt?> GetByIdAsync(Guid id, CancellationToken cancellationToken)
    {
        return await _context.Prompts
            .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);
    }

    public async Task<PagedResult<Prompt>> GetAllAsync(PromptFilterRequest request, CancellationToken cancellationToken)
    {
        var query = _context.Prompts.AsNoTracking();

        if (request.HasSearchTerm)
        {
            var searchTerm = request.SearchTerm!;
            query = query.Where(p =>
                p.Name.Value.Contains(searchTerm) ||
                (p.Description != null && p.Description.Contains(searchTerm)) ||
                p.SystemPromptContent.Value.Contains(searchTerm));
        }

        if (request.HasActiveFilter)
        {
            query = query.Where(p => p.IsActive == request.IsActive);
        }

        if (request.CreatedAfter.HasValue)
        {
            query = query.Where(p => p.CreatedAt >= request.CreatedAfter.Value);
        }

        if (request.CreatedBefore.HasValue)
        {
            query = query.Where(p => p.CreatedAt <= request.CreatedBefore.Value);
        }

        if (request.UpdatedAfter.HasValue)
        {
            query = query.Where(p => p.UpdatedAt >= request.UpdatedAfter.Value);
        }

        if (request.UpdatedBefore.HasValue)
        {
            query = query.Where(p => p.UpdatedAt <= request.UpdatedBefore.Value);
        }

        query = ApplySorting(query, request.SortBy, request.SortDescending);

        var totalCount = await query.CountAsync(cancellationToken);


        var items = await query
            .Skip(request.Skip)
            .Take(request.Take)
            .ToListAsync(cancellationToken);

        return PagedResult<Prompt>.Create(items, totalCount, request.PageNumber, request.PageSize);
    }

    public async Task<Prompt?> GetActivePromptAsync(CancellationToken cancellationToken)
    {
        return await _context.Prompts
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.IsActive, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, CancellationToken cancellationToken)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);

        return await _context.Prompts
            .AsNoTracking()
            .AnyAsync(p => p.Name.Value == name, cancellationToken);
    }

    public async Task<bool> ExistsByNameAsync(string name, Guid excludeId, CancellationToken cancellationToken)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(name);

        return await _context.Prompts
            .AsNoTracking()
            .AnyAsync(p => p.Name.Value == name && p.Id != excludeId, cancellationToken);
    }

    public async Task AddAsync(Prompt prompt, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(prompt);

        _context.Prompts.Add(prompt);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task UpdateAsync(Prompt prompt, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(prompt);

        _context.Prompts.Update(prompt);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteAsync(Prompt prompt, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(prompt);

        _context.Prompts.Remove(prompt);
        await _context.SaveChangesAsync(cancellationToken);
    }

    private static IQueryable<Prompt> ApplySorting(IQueryable<Prompt> query, string? sortBy, bool sortDescending)
    {
        return sortBy?.ToLower() switch
        {
            "name" => sortDescending
                ? query.OrderByDescending(p => p.Name).ThenByDescending(p => p.Id)
                : query.OrderBy(p => p.Name).ThenBy(p => p.Id),

            "description" => sortDescending
                ? query.OrderByDescending(p => p.Description).ThenByDescending(p => p.Id)
                : query.OrderBy(p => p.Description).ThenBy(p => p.Id),

            "isactive" => sortDescending
                ? query.OrderByDescending(p => p.IsActive).ThenByDescending(p => p.Id)
                : query.OrderBy(p => p.IsActive).ThenBy(p => p.Id),

            "updatedat" => sortDescending
                ? query.OrderByDescending(p => p.UpdatedAt).ThenByDescending(p => p.Id)
                : query.OrderBy(p => p.UpdatedAt).ThenBy(p => p.Id),

            "createdat" or _ => sortDescending
                ? query.OrderByDescending(p => p.CreatedAt).ThenByDescending(p => p.Id)
                : query.OrderBy(p => p.CreatedAt).ThenBy(p => p.Id)
        };
    }
}
