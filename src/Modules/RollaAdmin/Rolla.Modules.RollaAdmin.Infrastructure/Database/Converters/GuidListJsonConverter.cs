using System.Text.Json;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Rolla.Modules.RollaAdmin.Infrastructure.Database.Converters;

public class GuidListJsonConverter : ValueConverter<List<Guid>, string?>
{
    public GuidListJsonConverter() : base(
        v => v.Count == 0 ? "[]" : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
        v => string.IsNullOrEmpty(v) ? new List<Guid>() : JsonSerializer.Deserialize<List<Guid>>(v, (JsonSerializerOptions?)null) ?? new List<Guid>())
    {
    }
}
