using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace Rolla.API.Swagger;

public class ValidationDocumentationFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type == null) return;

        var properties = context.Type.GetProperties();

        foreach (var property in properties)
        {
            var propertyName = GetPropertyName(property);
            if (schema.Properties?.ContainsKey(propertyName) != true) continue;

            var propertySchema = schema.Properties[propertyName];

            AddValidationInfo(property, propertySchema);
        }

        AddSpecificTypeDocumentation(context.Type, schema);
    }

    private static string GetPropertyName(PropertyInfo property)
    {
        return char.ToLowerInvariant(property.Name[0]) + property.Name[1..];
    }

    private static void AddValidationInfo(PropertyInfo property, OpenApiSchema propertySchema)
    {
        var attributes = property.GetCustomAttributes();

        foreach (var attribute in attributes)
        {
            switch (attribute)
            {
                case RequiredAttribute required:
                    propertySchema.Description = AddToDescription(propertySchema.Description,
                        "**Required field**");
                    break;

                case MaxLengthAttribute maxLength:
                    propertySchema.Description = AddToDescription(propertySchema.Description,
                        $"**Maximum length**: {maxLength.Length} characters");
                    propertySchema.MaxLength = maxLength.Length;
                    break;

                case RangeAttribute range:
                    propertySchema.Description = AddToDescription(propertySchema.Description,
                        $"**Range**: {range.Minimum} to {range.Maximum}");
                    if (range.Minimum is int min) propertySchema.Minimum = min;
                    if (range.Maximum is int max) propertySchema.Maximum = max;
                    break;
            }
        }
    }

    private static void AddSpecificTypeDocumentation(Type type, OpenApiSchema schema)
    {
        if (type.Name.Contains("FilterRequest") || type.Name.Contains("PaginationRequest"))
        {
            schema.Description = AddToDescription(schema.Description,
                "\n\n**Pagination Rules:**\n" +
                "- Page number must be greater than 0\n" +
                "- Page size must be between 1 and 100\n" +
                "- Default page size is 10");
        }

        // Add PromptTrigger specific documentation
        if (type.Name.Contains("PromptTrigger"))
        {
            schema.Description = AddToDescription(schema.Description,
                "\n\n**PromptTrigger Validation Rules:**\n" +
                "- Priority: 1-1000 (lower numbers = higher priority)\n" +
                "- InsightNumber: 1-1000\n" +
                "- CooldownMinutes: 0-10080 (7 days max)\n" +
                "- DailyLimit: 1-100\n" +
                "- Conditional validation based on ConditionType");
        }
    }

    private static string AddToDescription(string? existing, string addition)
    {
        return string.IsNullOrEmpty(existing) ? addition : $"{existing}\n\n{addition}";
    }
}
