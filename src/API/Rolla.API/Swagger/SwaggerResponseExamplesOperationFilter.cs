using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Rolla.API.Swagger;

public class SwaggerResponseExamplesOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var controllerName = context.MethodInfo.DeclaringType?.Name;
        var actionName = context.MethodInfo.Name;

        if (controllerName == "PromptController")
        {
            AddPromptControllerExamples(operation, actionName);
            AddPromptControllerErrorExamples(operation, actionName);
        }
        else if (controllerName == "GuidelineController")
        {
            AddGuidelineControllerExamples(operation, actionName);
            AddGuidelineControllerErrorExamples(operation, actionName);
        }
        else if (controllerName == "TopicController")
        {
            AddTopicControllerExamples(operation, actionName);
            AddTopicControllerErrorExamples(operation, actionName);
        }
        else if (controllerName == "PromptTriggerController")
        {
            AddPromptTriggerControllerExamples(operation, actionName);
            AddPromptTriggerControllerErrorExamples(operation, actionName);
        }
        else
        {
            AddCommonErrorExamples(operation);
        }
    }

    private static void AddPromptControllerExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetPrompts":
                AddPaginatedPromptsExample(operation);
                break;
            case "GetPrompt":
            case "GetActivePrompt":
                AddSinglePromptExample(operation);
                break;
            case "CreatePrompt":
                AddCreatePromptExample(operation);
                break;
            case "UpdatePrompt":
                AddUpdatePromptExample(operation);
                break;
            case "ActivatePrompt":
                AddActivatePromptExample(operation);
                break;
        }
    }

    private static void AddPaginatedPromptsExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["items"] = new Microsoft.OpenApi.Any.OpenApiArray
                    {
                        new Microsoft.OpenApi.Any.OpenApiObject
                        {
                            ["id"] = new Microsoft.OpenApi.Any.OpenApiString("123e4567-e89b-12d3-a456-426614174000"),
                            ["name"] = new Microsoft.OpenApi.Any.OpenApiString("System Prompt v1.0"),
                            ["description"] =
                                new Microsoft.OpenApi.Any.OpenApiString("Main system prompt for AI interactions"),
                            ["systemPromptContent"] =
                                new Microsoft.OpenApi.Any.OpenApiString("You are a helpful AI assistant..."),
                            ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                            ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                            ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                        }
                    },
                    ["totalCount"] = new Microsoft.OpenApi.Any.OpenApiInteger(25),
                    ["pageNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["pageSize"] = new Microsoft.OpenApi.Any.OpenApiInteger(10),
                    ["totalPages"] = new Microsoft.OpenApi.Any.OpenApiInteger(3),
                    ["hasPreviousPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["hasNextPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true)
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddSinglePromptExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("123e4567-e89b-12d3-a456-426614174000"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("System Prompt v1.0"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Main system prompt for AI interactions"),
                    ["systemPromptContent"] = new Microsoft.OpenApi.Any.OpenApiString(
                        "You are a helpful AI assistant that provides accurate and helpful responses to user queries."),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreatePromptExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("201", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("987fcdeb-51a2-43d1-b456-426614174999"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("New Test Prompt"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("A newly created prompt for testing"),
                    ["systemPromptContent"] =
                        new Microsoft.OpenApi.Any.OpenApiString("You are a test assistant for validation purposes."),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdatePromptExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("123e4567-e89b-12d3-a456-426614174000"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Updated System Prompt v1.1"),
                    ["description"] =
                        new Microsoft.OpenApi.Any.OpenApiString("Updated main system prompt with improvements"),
                    ["systemPromptContent"] = new Microsoft.OpenApi.Any.OpenApiString(
                        "You are an enhanced AI assistant that provides accurate, helpful, and contextual responses."),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
            };
        }
    }

    private static void AddActivatePromptExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:50:00Z")
            };
        }
    }

    private static void AddPromptControllerErrorExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetPrompts":
                AddGetPromptsErrorExamples(operation);
                break;
            case "GetPrompt":
                AddGetPromptErrorExamples(operation);
                break;
            case "GetActivePrompt":
                AddGetActivePromptErrorExamples(operation);
                break;
            case "CreatePrompt":
                AddCreatePromptErrorExamples(operation);
                break;
            case "UpdatePrompt":
                AddUpdatePromptErrorExamples(operation);
                break;
            case "ActivatePrompt":
                AddActivatePromptErrorExamples(operation);
                break;
            case "DeletePrompt":
                AddDeletePromptErrorExamples(operation);
                break;
        }
    }

    private static void AddCommonErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Page number must be greater than 0"),
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "Sort field must be one of: Name, Description, IsActive, CreatedAt, UpdatedAt")
                },
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt not found")
                },
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("500", out var serverErrorResponse) &&
            serverErrorResponse.Content?.ContainsKey("application/json") == true)
        {
            serverErrorResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "An unexpected error occurred while processing your request")
                },
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetPromptsErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Page number must be greater than 0"),
                    new Microsoft.OpenApi.Any.OpenApiString("Page size must be between 1 and 100"),
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "Sort field must be one of: Name, Description, IsActive, CreatedAt, UpdatedAt"),
                    new Microsoft.OpenApi.Any.OpenApiString("CreatedAfter must be before CreatedBefore"),
                    new Microsoft.OpenApi.Any.OpenApiString("Search term cannot exceed 100 characters")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("500", out var serverErrorResponse) &&
            serverErrorResponse.Content?.ContainsKey("application/json") == true)
        {
            serverErrorResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "An error occurred while retrieving prompts from the database")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(500),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Internal server error"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetPromptErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetActivePromptErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("No active prompt found in the system")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("No active prompt found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreatePromptErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Name is required"),
                    new Microsoft.OpenApi.Any.OpenApiString("SystemPromptContent is required"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 200 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Description cannot exceed 500 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("SystemPromptContent cannot exceed 10000 characters")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "A prompt with the name 'System Prompt v1.0' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdatePromptErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt ID"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 200 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("SystemPromptContent cannot be empty")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "A prompt with the name 'Updated Prompt Name' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddActivatePromptErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddDeletePromptErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt ID"),
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "Cannot delete the currently active prompt. Please activate another prompt first.")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGuidelineControllerExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetGuidelines":
                AddPaginatedGuidelinesExample(operation);
                break;
            case "GetGuideline":
            case "GetActiveGuideline":
                AddSingleGuidelineExample(operation);
                break;
            case "CreateGuideline":
                AddCreateGuidelineExample(operation);
                break;
            case "UpdateGuideline":
                AddUpdateGuidelineExample(operation);
                break;
            case "ActivateGuideline":
                AddActivateGuidelineExample(operation);
                break;
        }
    }

    private static void AddPaginatedGuidelinesExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["items"] = new Microsoft.OpenApi.Any.OpenApiArray
                    {
                        new Microsoft.OpenApi.Any.OpenApiObject
                        {
                            ["id"] = new Microsoft.OpenApi.Any.OpenApiString("456e7890-e12b-34d5-a678-************"),
                            ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Content Guidelines v1.0"),
                            ["description"] =
                                new Microsoft.OpenApi.Any.OpenApiString("Main content guidelines for AI interactions"),
                            ["promptGuidelineContent"] = new Microsoft.OpenApi.Any.OpenApiString(
                                "1. Always provide accurate information\n2. Use clear language\n3. Be helpful and professional"),
                            ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                            ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                            ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                        }
                    },
                    ["totalCount"] = new Microsoft.OpenApi.Any.OpenApiInteger(15),
                    ["pageNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["pageSize"] = new Microsoft.OpenApi.Any.OpenApiInteger(10),
                    ["totalPages"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                    ["hasPreviousPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["hasNextPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true)
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddSingleGuidelineExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("456e7890-e12b-34d5-a678-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Content Guidelines v1.0"),
                    ["description"] =
                        new Microsoft.OpenApi.Any.OpenApiString("Main content guidelines for AI interactions"),
                    ["promptGuidelineContent"] = new Microsoft.OpenApi.Any.OpenApiString(
                        "1. Always provide accurate and helpful information\n2. Use clear and concise language\n3. Include examples when appropriate\n4. Maintain a professional tone"),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreateGuidelineExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("201", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("New Test Guidelines"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("A newly created guideline for testing"),
                    ["promptGuidelineContent"] = new Microsoft.OpenApi.Any.OpenApiString(
                        "1. Test all functionality thoroughly\n2. Document any issues found\n3. Provide clear feedback"),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdateGuidelineExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("456e7890-e12b-34d5-a678-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Updated Content Guidelines v1.1"),
                    ["description"] =
                        new Microsoft.OpenApi.Any.OpenApiString("Updated main content guidelines with improvements"),
                    ["promptGuidelineContent"] = new Microsoft.OpenApi.Any.OpenApiString(
                        "1. Always provide accurate and helpful information\n2. Use clear and concise language\n3. Include examples when appropriate\n4. Maintain a professional tone\n5. Follow updated formatting standards"),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
            };
        }
    }

    private static void AddActivateGuidelineExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:50:00Z")
            };
        }
    }

    private static void AddTopicControllerExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetAllTopics":
                AddPaginatedTopicsExample(operation);
                break;
            case "GetTopicById":
                AddSingleTopicExample(operation);
                break;
            case "GetActiveTopics":
                AddActiveTopicsExample(operation);
                break;
            case "CreateTopic":
                AddCreateTopicExample(operation);
                break;
            case "UpdateTopic":
                AddUpdateTopicExample(operation);
                break;
            case "ActivateTopic":
                AddActivateTopicExample(operation);
                break;
            case "DeactivateTopic":
                AddDeactivateTopicExample(operation);
                break;
        }
    }

    private static void AddPaginatedTopicsExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["items"] = new Microsoft.OpenApi.Any.OpenApiArray
                    {
                        new Microsoft.OpenApi.Any.OpenApiObject
                        {
                            ["id"] = new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************"),
                            ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Customer Support"),
                            ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Guidelines for handling customer support inquiries"),
                            ["prompt"] = new Microsoft.OpenApi.Any.OpenApiString("When handling customer support requests, always be polite, professional, and solution-oriented. Listen carefully to the customer's concerns and provide clear, actionable solutions."),
                            ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(100),
                            ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                            ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                            ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                        },
                        new Microsoft.OpenApi.Any.OpenApiObject
                        {
                            ["id"] = new Microsoft.OpenApi.Any.OpenApiString("456e7890-e12b-34d5-a678-************"),
                            ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Technical Documentation"),
                            ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Best practices for writing technical documentation"),
                            ["prompt"] = new Microsoft.OpenApi.Any.OpenApiString("When creating technical documentation, use clear language, provide examples, and structure information logically. Include code samples where appropriate."),
                            ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(200),
                            ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                            ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-10T08:15:00Z"),
                            ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-18T16:30:00Z")
                        }
                    },
                    ["totalCount"] = new Microsoft.OpenApi.Any.OpenApiInteger(35),
                    ["pageNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["pageSize"] = new Microsoft.OpenApi.Any.OpenApiInteger(10),
                    ["totalPages"] = new Microsoft.OpenApi.Any.OpenApiInteger(4),
                    ["hasPreviousPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["hasNextPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true)
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddSingleTopicExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Customer Support"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Guidelines for handling customer support inquiries"),
                    ["prompt"] = new Microsoft.OpenApi.Any.OpenApiString("When handling customer support requests, always be polite, professional, and solution-oriented. Listen carefully to the customer's concerns and provide clear, actionable solutions. Escalate complex issues when necessary."),
                    ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(100),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddActiveTopicsExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiObject
                    {
                        ["id"] = new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************"),
                        ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Customer Support"),
                        ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Guidelines for handling customer support inquiries"),
                        ["prompt"] = new Microsoft.OpenApi.Any.OpenApiString("When handling customer support requests, always be polite and professional."),
                        ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(100),
                        ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                        ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                        ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                    },
                    new Microsoft.OpenApi.Any.OpenApiObject
                    {
                        ["id"] = new Microsoft.OpenApi.Any.OpenApiString("123e4567-e89b-12d3-a456-************"),
                        ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Sales Conversations"),
                        ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Best practices for sales-related conversations"),
                        ["prompt"] = new Microsoft.OpenApi.Any.OpenApiString("Focus on understanding customer needs and providing value-driven solutions."),
                        ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(150),
                        ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                        ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-12T09:20:00Z"),
                        ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-19T11:15:00Z")
                    }
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreateTopicExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("201", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("987fcdeb-51a2-43d1-b456-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("New Product Training"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Training guidelines for new product features"),
                    ["prompt"] = new Microsoft.OpenApi.Any.OpenApiString("When training on new products, focus on key features, benefits, and common use cases. Provide hands-on examples and encourage questions."),
                    ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(300),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdateTopicExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Enhanced Customer Support"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Updated guidelines for handling customer support inquiries with new protocols"),
                    ["prompt"] = new Microsoft.OpenApi.Any.OpenApiString("When handling customer support requests, always be polite, professional, and solution-oriented. Listen carefully to the customer's concerns, provide clear actionable solutions, and follow up to ensure satisfaction."),
                    ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(50),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
            };
        }
    }

    private static void AddActivateTopicExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:50:00Z")
            };
        }
    }

    private static void AddDeactivateTopicExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:55:00Z")
            };
        }
    }

    private static void AddGuidelineControllerErrorExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetGuidelines":
                AddGetGuidelinesErrorExamples(operation);
                break;
            case "GetGuideline":
                AddGetGuidelineErrorExamples(operation);
                break;
            case "GetActiveGuideline":
                AddGetActiveGuidelineErrorExamples(operation);
                break;
            case "CreateGuideline":
                AddCreateGuidelineErrorExamples(operation);
                break;
            case "UpdateGuideline":
                AddUpdateGuidelineErrorExamples(operation);
                break;
            case "ActivateGuideline":
                AddActivateGuidelineErrorExamples(operation);
                break;
            case "DeleteGuideline":
                AddDeleteGuidelineErrorExamples(operation);
                break;
        }
    }

    private static void AddGetGuidelinesErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Page number must be greater than 0"),
                    new Microsoft.OpenApi.Any.OpenApiString("Page size must be between 1 and 100"),
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "Sort field must be one of: Name, Description, IsActive, CreatedAt, UpdatedAt"),
                    new Microsoft.OpenApi.Any.OpenApiString("CreatedAfter must be before CreatedBefore"),
                    new Microsoft.OpenApi.Any.OpenApiString("Search term cannot exceed 100 characters")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetGuidelineErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for guideline ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Guideline not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Guideline not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetActiveGuidelineErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("No active guideline found in the system")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("No active guideline found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreateGuidelineErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Name is required"),
                    new Microsoft.OpenApi.Any.OpenApiString("Guideline content is required"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 200 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Description cannot exceed 500 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Guideline content cannot exceed 10000 characters")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "A guideline with the name 'Content Guidelines v1.0' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdateGuidelineErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for guideline ID"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 200 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Guideline content cannot be empty if provided"),
                    new Microsoft.OpenApi.Any.OpenApiString("At least one field must be provided for update")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Guideline not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Guideline not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString(
                        "A guideline with the name 'Updated Guidelines Name' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddActivateGuidelineErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for guideline ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Guideline not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Guideline not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddDeleteGuidelineErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for guideline ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Guideline not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Guideline not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddPromptTriggerControllerExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetAllPromptTriggers":
                AddPaginatedPromptTriggersExample(operation);
                break;
            case "GetPromptTriggerById":
                AddSinglePromptTriggerExample(operation);
                break;
            case "CreatePromptTrigger":
                AddCreatePromptTriggerExample(operation);
                break;
            case "UpdatePromptTrigger":
                AddUpdatePromptTriggerExample(operation);
                break;
            case "ActivatePromptTrigger":
                AddActivatePromptTriggerExample(operation);
                break;
            case "DeactivatePromptTrigger":
                AddDeactivatePromptTriggerExample(operation);
                break;
        }
    }

    private static void AddTopicControllerErrorExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetAllTopics":
                AddGetTopicsErrorExamples(operation);
                break;
            case "GetTopicById":
                AddGetTopicErrorExamples(operation);
                break;
            case "GetActiveTopics":
                AddGetActiveTopicsErrorExamples(operation);
                break;
            case "CreateTopic":
                AddCreateTopicErrorExamples(operation);
                break;
            case "UpdateTopic":
                AddUpdateTopicErrorExamples(operation);
                break;
            case "ActivateTopic":
                AddActivateTopicErrorExamples(operation);
                break;
            case "DeactivateTopic":
                AddDeactivateTopicErrorExamples(operation);
                break;
            case "DeleteTopic":
                AddDeleteTopicErrorExamples(operation);
                break;
        }
    }

    private static void AddGetTopicsErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Page number must be greater than 0"),
                    new Microsoft.OpenApi.Any.OpenApiString("Page size must be between 1 and 100"),
                    new Microsoft.OpenApi.Any.OpenApiString("Sort field must be one of: Name, Description, Priority, IsActive, CreatedAt, UpdatedAt"),
                    new Microsoft.OpenApi.Any.OpenApiString("CreatedAfter must be before CreatedBefore"),
                    new Microsoft.OpenApi.Any.OpenApiString("Search term cannot exceed 100 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Minimum priority must be non-negative"),
                    new Microsoft.OpenApi.Any.OpenApiString("Maximum priority must be greater than minimum priority")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("500", out var serverErrorResponse) &&
            serverErrorResponse.Content?.ContainsKey("application/json") == true)
        {
            serverErrorResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("An error occurred while retrieving topics from the database")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(500),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Internal server error"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetTopicErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for topic ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Topic not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Topic not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetActiveTopicsErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("500", out var serverErrorResponse) &&
            serverErrorResponse.Content?.ContainsKey("application/json") == true)
        {
            serverErrorResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("An error occurred while retrieving active topics")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(500),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Internal server error"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreateTopicErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Name is required"),
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt is required"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 100 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Description cannot exceed 500 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt cannot exceed 5000 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Priority must be between 0 and 1000")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("A topic with the name 'Customer Support' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdateTopicErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for topic ID"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 100 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt cannot be empty if provided"),
                    new Microsoft.OpenApi.Any.OpenApiString("At least one field must be provided for update"),
                    new Microsoft.OpenApi.Any.OpenApiString("Priority must be between 0 and 1000")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Topic not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Topic not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("A topic with the name 'Updated Topic Name' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddActivateTopicErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for topic ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Topic not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Topic not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddDeactivateTopicErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for topic ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Topic not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Topic not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddDeleteTopicErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for topic ID"),
                    new Microsoft.OpenApi.Any.OpenApiString("Cannot delete an active topic. Please deactivate it first.")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Topic not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Topic not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddPaginatedPromptTriggersExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["items"] = new Microsoft.OpenApi.Any.OpenApiArray
                    {
                        new Microsoft.OpenApi.Any.OpenApiObject
                        {
                            ["id"] = new Microsoft.OpenApi.Any.OpenApiString("123e4567-e89b-12d3-a456-************"),
                            ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Morning Health Check"),
                            ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Trigger for morning health score evaluation"),
                            ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(100),
                            ["insightNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                            ["cooldownMinutes"] = new Microsoft.OpenApi.Any.OpenApiInteger(480),
                            ["dailyLimit"] = new Microsoft.OpenApi.Any.OpenApiInteger(3),
                            ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                            ["triggerCondition"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                            ["conditionType"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                            ["scoreType"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                            ["operator"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                            ["value"] = new Microsoft.OpenApi.Any.OpenApiString("75"),
                            ["startTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                            ["endTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                            ["metricType"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                            ["associatedTopicIds"] = new Microsoft.OpenApi.Any.OpenApiArray
                            {
                                new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************")
                            },
                            ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                            ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                        },
                        new Microsoft.OpenApi.Any.OpenApiObject
                        {
                            ["id"] = new Microsoft.OpenApi.Any.OpenApiString("456e7890-e12b-34d5-a678-************"),
                            ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Evening Sleep Reminder"),
                            ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Trigger for bedtime routine based on time"),
                            ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(200),
                            ["insightNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                            ["cooldownMinutes"] = new Microsoft.OpenApi.Any.OpenApiInteger(1440),
                            ["dailyLimit"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                            ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                            ["triggerCondition"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                            ["conditionType"] = new Microsoft.OpenApi.Any.OpenApiInteger(3),
                            ["scoreType"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                            ["operator"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                            ["value"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                            ["startTime"] = new Microsoft.OpenApi.Any.OpenApiString("21:00:00"),
                            ["endTime"] = new Microsoft.OpenApi.Any.OpenApiString("22:30:00"),
                            ["metricType"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                            ["associatedTopicIds"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                            ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-10T08:15:00Z"),
                            ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-18T16:30:00Z")
                        }
                    },
                    ["totalCount"] = new Microsoft.OpenApi.Any.OpenApiInteger(25),
                    ["pageNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["pageSize"] = new Microsoft.OpenApi.Any.OpenApiInteger(10),
                    ["totalPages"] = new Microsoft.OpenApi.Any.OpenApiInteger(3),
                    ["hasPreviousPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["hasNextPage"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true)
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddSinglePromptTriggerExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("123e4567-e89b-12d3-a456-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Morning Health Check"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Comprehensive trigger for morning health score evaluation and insights"),
                    ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(100),
                    ["insightNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["cooldownMinutes"] = new Microsoft.OpenApi.Any.OpenApiInteger(480),
                    ["dailyLimit"] = new Microsoft.OpenApi.Any.OpenApiInteger(3),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["triggerCondition"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["conditionType"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                    ["scoreType"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["operator"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                    ["value"] = new Microsoft.OpenApi.Any.OpenApiString("75"),
                    ["startTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["endTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["metricType"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["associatedTopicIds"] = new Microsoft.OpenApi.Any.OpenApiArray
                    {
                        new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************"),
                        new Microsoft.OpenApi.Any.OpenApiString("456e7890-e12b-34d5-a678-************")
                    },
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T14:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreatePromptTriggerExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("201", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("987fcdeb-51a2-43d1-b456-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Step Count Achievement"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Trigger when daily step count reaches target"),
                    ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(150),
                    ["insightNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(3),
                    ["cooldownMinutes"] = new Microsoft.OpenApi.Any.OpenApiInteger(240),
                    ["dailyLimit"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                    ["triggerCondition"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["conditionType"] = new Microsoft.OpenApi.Any.OpenApiInteger(4),
                    ["scoreType"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["operator"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                    ["value"] = new Microsoft.OpenApi.Any.OpenApiString("10000"),
                    ["startTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["endTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["metricType"] = new Microsoft.OpenApi.Any.OpenApiInteger(4),
                    ["associatedTopicIds"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdatePromptTriggerExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiObject
                {
                    ["id"] = new Microsoft.OpenApi.Any.OpenApiString("123e4567-e89b-12d3-a456-************"),
                    ["name"] = new Microsoft.OpenApi.Any.OpenApiString("Enhanced Morning Health Check"),
                    ["description"] = new Microsoft.OpenApi.Any.OpenApiString("Updated comprehensive trigger for morning health score evaluation with new thresholds"),
                    ["priority"] = new Microsoft.OpenApi.Any.OpenApiInteger(50),
                    ["insightNumber"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["cooldownMinutes"] = new Microsoft.OpenApi.Any.OpenApiInteger(360),
                    ["dailyLimit"] = new Microsoft.OpenApi.Any.OpenApiInteger(5),
                    ["isActive"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                    ["triggerCondition"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["conditionType"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                    ["scoreType"] = new Microsoft.OpenApi.Any.OpenApiInteger(1),
                    ["operator"] = new Microsoft.OpenApi.Any.OpenApiInteger(2),
                    ["value"] = new Microsoft.OpenApi.Any.OpenApiString("80"),
                    ["startTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["endTime"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["metricType"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                    ["associatedTopicIds"] = new Microsoft.OpenApi.Any.OpenApiArray
                    {
                        new Microsoft.OpenApi.Any.OpenApiString("789abcde-f23c-45e6-b789-************")
                    },
                    ["createdAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-15T10:30:00Z"),
                    ["updatedAt"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
                },
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:45:00Z")
            };
        }
    }

    private static void AddActivatePromptTriggerExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:50:00Z")
            };
        }
    }

    private static void AddDeactivatePromptTriggerExample(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("200", out var response) &&
            response.Content?.ContainsKey("application/json") == true)
        {
            response.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiBoolean(true),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray(),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T16:55:00Z")
            };
        }
    }

    private static void AddPromptTriggerControllerErrorExamples(OpenApiOperation operation, string actionName)
    {
        switch (actionName)
        {
            case "GetAllPromptTriggers":
                AddGetPromptTriggersErrorExamples(operation);
                break;
            case "GetPromptTriggerById":
                AddGetPromptTriggerErrorExamples(operation);
                break;
            case "CreatePromptTrigger":
                AddCreatePromptTriggerErrorExamples(operation);
                break;
            case "UpdatePromptTrigger":
                AddUpdatePromptTriggerErrorExamples(operation);
                break;
            case "ActivatePromptTrigger":
                AddActivatePromptTriggerErrorExamples(operation);
                break;
            case "DeactivatePromptTrigger":
                AddDeactivatePromptTriggerErrorExamples(operation);
                break;
            case "DeletePromptTrigger":
                AddDeletePromptTriggerErrorExamples(operation);
                break;
        }
    }

    private static void AddGetPromptTriggersErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Page number must be greater than 0"),
                    new Microsoft.OpenApi.Any.OpenApiString("Page size must be between 1 and 100"),
                    new Microsoft.OpenApi.Any.OpenApiString("Sort field must be one of: Name, Description, Priority, InsightNumber, CooldownMinutes, DailyLimit, IsActive, TriggerCondition, ConditionType, CreatedAt, UpdatedAt"),
                    new Microsoft.OpenApi.Any.OpenApiString("CreatedAfter must be before CreatedBefore"),
                    new Microsoft.OpenApi.Any.OpenApiString("Search term cannot exceed 100 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Minimum priority must be non-negative"),
                    new Microsoft.OpenApi.Any.OpenApiString("Maximum priority must be greater than minimum priority")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("500", out var serverErrorResponse) &&
            serverErrorResponse.Content?.ContainsKey("application/json") == true)
        {
            serverErrorResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("An error occurred while retrieving prompt triggers from the database")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(500),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Internal server error"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddGetPromptTriggerErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt trigger ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddCreatePromptTriggerErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Name is required"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 100 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Description cannot exceed 500 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Priority must be between 1 and 1000"),
                    new Microsoft.OpenApi.Any.OpenApiString("Insight number must be between 1 and 1000"),
                    new Microsoft.OpenApi.Any.OpenApiString("Cooldown minutes must be between 0 and 10080 (7 days)"),
                    new Microsoft.OpenApi.Any.OpenApiString("Daily limit must be between 1 and 100"),
                    new Microsoft.OpenApi.Any.OpenApiString("Score type is required when condition type is ScoreValue"),
                    new Microsoft.OpenApi.Any.OpenApiString("Operator is required when condition type is ScoreValue or MetricValue"),
                    new Microsoft.OpenApi.Any.OpenApiString("Value is required when operator is not ChangedDuringDay"),
                    new Microsoft.OpenApi.Any.OpenApiString("Start time is required when condition type is TimeOfDay"),
                    new Microsoft.OpenApi.Any.OpenApiString("End time is required when condition type is TimeOfDay"),
                    new Microsoft.OpenApi.Any.OpenApiString("Start time must be before end time"),
                    new Microsoft.OpenApi.Any.OpenApiString("Metric type is required when condition type is MetricValue")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("A prompt trigger with the name 'Morning Health Check' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddUpdatePromptTriggerErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt trigger ID"),
                    new Microsoft.OpenApi.Any.OpenApiString("At least one field must be provided for update"),
                    new Microsoft.OpenApi.Any.OpenApiString("Name cannot exceed 100 characters"),
                    new Microsoft.OpenApi.Any.OpenApiString("Priority must be between 1 and 1000"),
                    new Microsoft.OpenApi.Any.OpenApiString("Changing condition type is not supported. Please create a new prompt trigger.")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("409", out var conflictResponse) &&
            conflictResponse.Content?.ContainsKey("application/json") == true)
        {
            conflictResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("A prompt trigger with the name 'Updated Trigger Name' already exists")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(409),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Conflict"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddActivatePromptTriggerErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt trigger ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddDeactivatePromptTriggerErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt trigger ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }

    private static void AddDeletePromptTriggerErrorExamples(OpenApiOperation operation)
    {
        if (operation.Responses.TryGetValue("400", out var badRequestResponse) &&
            badRequestResponse.Content?.ContainsKey("application/json") == true)
        {
            badRequestResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Invalid GUID format for prompt trigger ID")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(400),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Bad request"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }

        if (operation.Responses.TryGetValue("404", out var notFoundResponse) &&
            notFoundResponse.Content?.ContainsKey("application/json") == true)
        {
            notFoundResponse.Content["application/json"].Example = new Microsoft.OpenApi.Any.OpenApiObject
            {
                ["success"] = new Microsoft.OpenApi.Any.OpenApiBoolean(false),
                ["data"] = new Microsoft.OpenApi.Any.OpenApiNull(),
                ["errors"] = new Microsoft.OpenApi.Any.OpenApiArray
                {
                    new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found")
                },
                ["statusCode"] = new Microsoft.OpenApi.Any.OpenApiInteger(404),
                ["message"] = new Microsoft.OpenApi.Any.OpenApiString("Prompt trigger not found"),
                ["timestamp"] = new Microsoft.OpenApi.Any.OpenApiString("2024-01-20T15:30:00Z")
            };
        }
    }
}
