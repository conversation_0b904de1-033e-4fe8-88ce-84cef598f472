{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/rolla-api-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}, "ConnectionStrings": {"DefaultConnection": ""}, "AllowedHosts": "*", "RollaAdmin": {"EnableSwagger": true, "EnableDetailedErrors": true}, "PromptSettings": {"Pagination": {"DefaultPageSize": 10, "MaxPageSize": 100, "MinPageSize": 1}, "Cache": {"ActivePromptCacheMinutes": 30, "EnableCaching": false}, "Search": {"MaxSearchTermLength": 100, "EnableFullTextSearch": false, "SearchableFields": ["Name", "Description", "SystemPromptContent"]}}}