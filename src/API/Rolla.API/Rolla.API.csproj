<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.13">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\BuildingBlocks\Rolla.BuildingBlocks.Application\Rolla.BuildingBlocks.Application.csproj" />
        <ProjectReference Include="..\..\BuildingBlocks\Rolla.BuildingBlocks.Infrastructure\Rolla.BuildingBlocks.Infrastructure.csproj" />
        <ProjectReference Include="..\..\Modules\RollaAdmin\Rolla.Modules.RollaAdmin.Api\Rolla.Modules.RollaAdmin.Api.csproj" />
    </ItemGroup>

</Project>
